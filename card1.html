<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Car1</title>
    <style>
      * {
        margin: 0px;
        padding: 0px;
        box-sizing: border-box;
      }
      .container {
        width: 100vw;
        height: 100vh;
        background-color: rgb(221, 227, 235);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .card {
        width: 400px;
        height: 550px;
        background-color: white;
        border-radius: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
      }
      .card img {
        width: 100%;
        height: 30%;
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;
      }
      .text-box{

        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
        width: 70%;
        text-align: center;

        
        
        
      }
      #amount-box{

        width: 80%;
        height:10%;
        background-color: rgb(40, 82, 161);
        border-radius: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 20px;
        padding-left: 20px;

      }
      #paymentBtn{

        width: 80%;
        height: 9%;
        background-color: rgb(13, 13, 121);
        color: white;
        border-radius: 10px;
      }
      .cancelBox a{
text-decoration: none;
color: rgb(54, 52, 52);
font-size: large;

      }
      #leftbox{

 width: fit-content;
 display: flex;
 gap: 15px;
 height: 100% ;
 align-items: center;
 color: white;

      }
      .imgbox img{

        height: 3rem;
        width: 3rem;
      }
      .rightbox a{
        text-decoration: none;
        color: white;

       


      }
    </style>
  </head>

  <body>
    <div class="container">
      <div class="card">
        <img src="img.png" alt="" />
        <div class="text-box">
          <h2>Order Summary</h2>
          <p>
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Hic
            corporis amet facere reiciendis at eligendi placeat quae excepturi
            qui temporibus.
          </p>
        </div>
        <div id="amount-box"> 
          <div id="leftbox">
            <div class="imgbox">
              <img src="img.png" alt="">

            </div>
            <div id="amountbox">
              <p>Amout</p>
              <p>99$/yearly</p>

            </div>

          </div>
          <div class="rightbox">
            <a href="#">Change</a>
          </div>

        </div>
        <button id="paymentBtn">
          procedue to payment

        </button>
        <div class="cancelBox">
          <a href="#">Cancel Order</a>
        </div>


      </div>
    </div>
  </body>
</html>
