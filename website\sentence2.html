<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Advanced Sentence Highlighter Pro</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
  <style>
    :root {
      --primary-color: #4f46e5;
      --primary-hover: #3730a3;
      --secondary-color: #06b6d4;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --danger-color: #ef4444;

      --bg-light: #ffffff;
      --bg-light-secondary: #f8fafc;
      --bg-dark: #0f172a;
      --bg-dark-secondary: #1e293b;

      --text-light: #1e293b;
      --text-light-secondary: #64748b;
      --text-dark: #f1f5f9;
      --text-dark-secondary: #cbd5e1;

      --border-light: #e2e8f0;
      --border-dark: #334155;

      --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: var(--bg-light);
      color: var(--text-light);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      line-height: 1.6;
    }

    .dark-mode {
      background: var(--bg-dark);
      color: var(--text-dark);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .header {
      text-align: center;
      margin-bottom: 2rem;
      padding: 2rem 0;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: 16px;
      color: white;
      box-shadow: var(--shadow-large);
    }

    .header h1 {
      margin: 0;
      font-size: 2.5rem;
      font-weight: 700;
      background: linear-gradient(45deg, #ffffff, #e0e7ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .header p {
      margin: 0.5rem 0 0 0;
      opacity: 0.9;
      font-size: 1.1rem;
    }

    .main-content {
      display: grid;
      grid-template-columns: 1fr 300px;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
      .main-content {
        grid-template-columns: 1fr;
      }

      .header h1 {
        font-size: 2rem;
      }

      body {
        padding: 10px;
      }
    }

    .input-section {
      background: var(--bg-light-secondary);
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: var(--shadow-medium);
      border: 1px solid var(--border-light);
    }

    .dark-mode .input-section {
      background: var(--bg-dark-secondary);
      border-color: var(--border-dark);
    }

    .input-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .input-header h3 {
      margin: 0;
      color: var(--primary-color);
      font-size: 1.25rem;
    }

    .char-counter {
      font-size: 0.875rem;
      color: var(--text-light-secondary);
    }

    .dark-mode .char-counter {
      color: var(--text-dark-secondary);
    }

    textarea {
      width: 100%;
      min-height: 200px;
      padding: 1rem;
      font-size: 16px;
      border-radius: 8px;
      border: 2px solid var(--border-light);
      background: var(--bg-light);
      color: var(--text-light);
      resize: vertical;
      transition: all 0.3s ease;
      font-family: inherit;
      line-height: 1.6;
    }

    textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .dark-mode textarea {
      background: var(--bg-dark);
      color: var(--text-dark);
      border-color: var(--border-dark);
    }

    .dark-mode textarea:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
    }

    .button-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 0.75rem;
      margin-top: 1rem;
    }

    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      font-size: 0.875rem;
      font-weight: 500;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      position: relative;
      overflow: hidden;
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .btn-primary {
      background: var(--primary-color);
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: var(--primary-hover);
      transform: translateY(-1px);
      box-shadow: var(--shadow-medium);
    }

    .btn-secondary {
      background: var(--secondary-color);
      color: white;
    }

    .btn-secondary:hover:not(:disabled) {
      background: #0891b2;
      transform: translateY(-1px);
      box-shadow: var(--shadow-medium);
    }

    .btn-success {
      background: var(--success-color);
      color: white;
    }

    .btn-success:hover:not(:disabled) {
      background: #059669;
      transform: translateY(-1px);
      box-shadow: var(--shadow-medium);
    }

    .btn-outline {
      background: transparent;
      color: var(--text-light);
      border: 2px solid var(--border-light);
    }

    .btn-outline:hover:not(:disabled) {
      background: var(--primary-color);
      color: white;
      border-color: var(--primary-color);
    }

    .dark-mode .btn-outline {
      color: var(--text-dark);
      border-color: var(--border-dark);
    }

    .file-input-wrapper {
      position: relative;
      overflow: hidden;
      display: inline-block;
      width: 100%;
    }

    .file-input-wrapper input[type=file] {
      position: absolute;
      left: -9999px;
    }

    .sidebar {
      background: var(--bg-light-secondary);
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: var(--shadow-medium);
      border: 1px solid var(--border-light);
      height: fit-content;
      position: sticky;
      top: 20px;
    }

    .dark-mode .sidebar {
      background: var(--bg-dark-secondary);
      border-color: var(--border-dark);
    }

    .sidebar h3 {
      margin: 0 0 1rem 0;
      color: var(--primary-color);
      font-size: 1.25rem;
    }

    .stats-grid {
      display: grid;
      gap: 1rem;
    }

    .stat-card {
      background: var(--bg-light);
      border: 1px solid var(--border-light);
      border-radius: 8px;
      padding: 1rem;
      text-align: center;
    }

    .dark-mode .stat-card {
      background: var(--bg-dark);
      border-color: var(--border-dark);
    }

    .stat-value {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: 0.875rem;
      color: var(--text-light-secondary);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .dark-mode .stat-label {
      color: var(--text-dark-secondary);
    }

    .output-section {
      background: var(--bg-light-secondary);
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: var(--shadow-medium);
      border: 1px solid var(--border-light);
      margin-top: 2rem;
    }

    .dark-mode .output-section {
      background: var(--bg-dark-secondary);
      border-color: var(--border-dark);
    }

    .output-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
    }

    .output-header h3 {
      margin: 0;
      color: var(--primary-color);
      font-size: 1.25rem;
    }

    .view-toggle {
      display: flex;
      background: var(--bg-light);
      border-radius: 8px;
      padding: 0.25rem;
      border: 1px solid var(--border-light);
    }

    .dark-mode .view-toggle {
      background: var(--bg-dark);
      border-color: var(--border-dark);
    }

    .view-toggle button {
      padding: 0.5rem 1rem;
      border: none;
      background: transparent;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.875rem;
    }

    .view-toggle button.active {
      background: var(--primary-color);
      color: white;
    }

    #output {
      font-size: 16px;
      line-height: 1.8;
      min-height: 200px;
    }

    .sentence {
      display: block;
      margin: 0.75rem 0;
      padding: 1rem;
      border-radius: 8px;
      border-left: 4px solid transparent;
      transition: all 0.3s ease;
      position: relative;
      cursor: pointer;
    }

    .sentence:hover {
      transform: translateX(4px);
      box-shadow: var(--shadow-medium);
    }

    .sentence-number {
      position: absolute;
      top: 0.5rem;
      right: 0.75rem;
      font-size: 0.75rem;
      opacity: 0.6;
      font-weight: 600;
    }

    .sentence-length-short {
      border-left-color: var(--success-color);
    }

    .sentence-length-medium {
      border-left-color: var(--warning-color);
    }

    .sentence-length-long {
      border-left-color: var(--danger-color);
    }

    .search-container {
      position: relative;
      margin-bottom: 1rem;
    }

    .search-input {
      width: 100%;
      padding: 0.75rem 1rem 0.75rem 2.5rem;
      border: 2px solid var(--border-light);
      border-radius: 8px;
      background: var(--bg-light);
      color: var(--text-light);
      font-size: 0.875rem;
    }

    .search-input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .dark-mode .search-input {
      background: var(--bg-dark);
      color: var(--text-dark);
      border-color: var(--border-dark);
    }

    .search-icon {
      position: absolute;
      left: 0.75rem;
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-light-secondary);
    }

    .dark-mode .search-icon {
      color: var(--text-dark-secondary);
    }

    .highlight-match {
      background: yellow;
      color: black;
      padding: 0.125rem 0.25rem;
      border-radius: 3px;
    }

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      color: var(--text-light-secondary);
    }

    .loading i {
      animation: spin 1s linear infinite;
      margin-right: 0.5rem;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .toast {
      position: fixed;
      top: 20px;
      right: 20px;
      background: var(--success-color);
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      box-shadow: var(--shadow-large);
      transform: translateX(100%);
      transition: transform 0.3s ease;
      z-index: 1000;
    }

    .toast.show {
      transform: translateX(0);
    }

    .toast.error {
      background: var(--danger-color);
    }

    .keyboard-shortcuts {
      margin-top: 1.5rem;
      padding-top: 1.5rem;
      border-top: 1px solid var(--border-light);
    }

    .dark-mode .keyboard-shortcuts {
      border-color: var(--border-dark);
    }

    .shortcut {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
      font-size: 0.875rem;
    }

    .shortcut-key {
      background: var(--bg-light);
      border: 1px solid var(--border-light);
      border-radius: 4px;
      padding: 0.25rem 0.5rem;
      font-family: monospace;
      font-size: 0.75rem;
    }

    .dark-mode .shortcut-key {
      background: var(--bg-dark);
      border-color: var(--border-dark);
    }

    /* Advanced Features Styles */
    .tabs {
      display: flex;
      background: var(--bg-light);
      border-radius: 8px;
      padding: 0.25rem;
      margin-bottom: 1.5rem;
      border: 1px solid var(--border-light);
    }

    .dark-mode .tabs {
      background: var(--bg-dark);
      border-color: var(--border-dark);
    }

    .tab {
      flex: 1;
      padding: 0.75rem 1rem;
      border: none;
      background: transparent;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--text-light-secondary);
    }

    .tab.active {
      background: var(--primary-color);
      color: white;
      box-shadow: var(--shadow-medium);
    }

    .dark-mode .tab {
      color: var(--text-dark-secondary);
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .analytics-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
      .analytics-grid {
        grid-template-columns: 1fr;
      }
    }

    .chart-container {
      background: var(--bg-light-secondary);
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: var(--shadow-medium);
      border: 1px solid var(--border-light);
    }

    .dark-mode .chart-container {
      background: var(--bg-dark-secondary);
      border-color: var(--border-dark);
    }

    .chart-container h4 {
      margin: 0 0 1rem 0;
      color: var(--primary-color);
      font-size: 1.1rem;
    }

    .readability-score {
      text-align: center;
      padding: 2rem;
    }

    .score-circle {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      font-size: 2rem;
      font-weight: 700;
      color: white;
      position: relative;
    }

    .score-excellent { background: linear-gradient(135deg, #10b981, #059669); }
    .score-good { background: linear-gradient(135deg, #f59e0b, #d97706); }
    .score-fair { background: linear-gradient(135deg, #ef4444, #dc2626); }

    .ai-insights {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
    }

    .ai-insights h4 {
      margin: 0 0 1rem 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .insight-item {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 0.75rem;
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    .insight-item:last-child {
      margin-bottom: 0;
    }

    .export-options {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-top: 1rem;
    }

    .export-card {
      background: var(--bg-light);
      border: 2px solid var(--border-light);
      border-radius: 8px;
      padding: 1rem;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .export-card:hover {
      border-color: var(--primary-color);
      transform: translateY(-2px);
      box-shadow: var(--shadow-medium);
    }

    .dark-mode .export-card {
      background: var(--bg-dark);
      border-color: var(--border-dark);
    }

    .export-card i {
      font-size: 2rem;
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    .language-selector {
      margin-bottom: 1rem;
    }

    .language-selector select {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid var(--border-light);
      border-radius: 8px;
      background: var(--bg-light);
      color: var(--text-light);
      font-size: 0.875rem;
    }

    .dark-mode .language-selector select {
      background: var(--bg-dark);
      color: var(--text-dark);
      border-color: var(--border-dark);
    }

    .collaboration-panel {
      background: var(--bg-light-secondary);
      border-radius: 12px;
      padding: 1.5rem;
      margin-top: 1.5rem;
      border: 1px solid var(--border-light);
    }

    .dark-mode .collaboration-panel {
      background: var(--bg-dark-secondary);
      border-color: var(--border-dark);
    }

    .version-history {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid var(--border-light);
      border-radius: 8px;
      padding: 1rem;
    }

    .dark-mode .version-history {
      border-color: var(--border-dark);
    }

    .version-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--border-light);
    }

    .version-item:last-child {
      border-bottom: none;
    }

    .dark-mode .version-item {
      border-color: var(--border-dark);
    }

    .progress-bar {
      width: 100%;
      height: 8px;
      background: var(--border-light);
      border-radius: 4px;
      overflow: hidden;
      margin: 0.5rem 0;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      transition: width 0.3s ease;
    }

    .dark-mode .progress-bar {
      background: var(--border-dark);
    }

    .floating-toolbar {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: var(--bg-light);
      border-radius: 50px;
      padding: 0.75rem;
      box-shadow: var(--shadow-large);
      border: 1px solid var(--border-light);
      display: flex;
      gap: 0.5rem;
      z-index: 1000;
      transform: translateY(100px);
      transition: transform 0.3s ease;
    }

    .floating-toolbar.show {
      transform: translateY(0);
    }

    .dark-mode .floating-toolbar {
      background: var(--bg-dark);
      border-color: var(--border-dark);
    }

    .floating-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: none;
      background: var(--primary-color);
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    .floating-btn:hover {
      transform: scale(1.1);
      background: var(--primary-hover);
    }

    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2000;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .modal.show {
      opacity: 1;
      visibility: visible;
    }

    .modal-content {
      background: var(--bg-light);
      border-radius: 12px;
      padding: 2rem;
      max-width: 600px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      transform: scale(0.9);
      transition: transform 0.3s ease;
    }

    .modal.show .modal-content {
      transform: scale(1);
    }

    .dark-mode .modal-content {
      background: var(--bg-dark);
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid var(--border-light);
    }

    .dark-mode .modal-header {
      border-color: var(--border-dark);
    }

    .close-modal {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: var(--text-light-secondary);
    }

    .dark-mode .close-modal {
      color: var(--text-dark-secondary);
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="header">
      <h1><i class="fas fa-highlighter"></i> Sentence Highlighter Pro</h1>
      <p>Advanced text analysis and sentence highlighting tool</p>
    </header>

    <!-- Advanced Tabs Navigation -->
    <div class="tabs">
      <button type="button" class="tab active" onclick="switchTab('editor')">
        <i class="fas fa-edit"></i> Editor
      </button>
      <button type="button" class="tab" onclick="switchTab('analytics')">
        <i class="fas fa-chart-line"></i> Analytics
      </button>
      <button type="button" class="tab" onclick="switchTab('export')">
        <i class="fas fa-download"></i> Export
      </button>
      <button type="button" class="tab" onclick="switchTab('collaboration')">
        <i class="fas fa-users"></i> Collaborate
      </button>
      <button type="button" class="tab" onclick="switchTab('settings')">
        <i class="fas fa-cog"></i> Settings
      </button>
    </div>

    <div class="main-content">
      <!-- Editor Tab -->
      <div id="editor-tab" class="tab-content active">
        <div class="input-section">
          <div class="input-header">
            <h3><i class="fas fa-edit"></i> Smart Text Editor</h3>
            <span class="char-counter" id="charCounter">0 characters</span>
          </div>

          <div class="language-selector">
            <label for="languageSelect" class="form-label">Language:</label>
            <select id="languageSelect" onchange="changeLanguage()" title="Select language for text analysis">
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="it">Italian</option>
              <option value="pt">Portuguese</option>
              <option value="ru">Russian</option>
              <option value="zh">Chinese</option>
              <option value="ja">Japanese</option>
              <option value="ar">Arabic</option>
            </select>
          </div>

          <div class="search-container">
            <i class="fas fa-search search-icon"></i>
            <input type="text" class="search-input" id="searchInput" placeholder="Search in text... (Supports regex)">
          </div>

          <textarea id="inputText" placeholder="Paste or type your text here... Try some sample text to see the magic happen!"></textarea>

          <div class="button-grid">
            <button type="button" class="btn btn-primary" onclick="highlightText()">
              <i class="fas fa-highlighter"></i> AI Analyze
            </button>
            <button type="button" class="btn btn-secondary" onclick="copyFormatted()">
              <i class="fas fa-copy"></i> Copy Text
            </button>
            <button type="button" class="btn btn-success" onclick="downloadText()">
              <i class="fas fa-download"></i> Download
            </button>
            <div class="file-input-wrapper">
              <input type="file" id="fileInput" accept=".txt,.md,.docx,.pdf" onchange="loadFile(event)" title="Select a text file to load" aria-label="Select a text file to load">
              <button type="button" class="btn btn-outline" onclick="document.getElementById('fileInput').click()">
                <i class="fas fa-upload"></i> Load File
              </button>
            </div>
            <button type="button" class="btn btn-outline" onclick="undoAction()">
              <i class="fas fa-undo"></i> Undo
            </button>
            <button type="button" class="btn btn-outline" onclick="redoAction()">
              <i class="fas fa-redo"></i> Redo
            </button>
            <button type="button" class="btn btn-outline" onclick="clearText()">
              <i class="fas fa-trash"></i> Clear
            </button>
            <button type="button" class="btn btn-outline" onclick="toggleDarkMode()">
              <i class="fas fa-moon" id="themeIcon"></i> <span id="themeText">Dark Mode</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Analytics Tab -->
      <div id="analytics-tab" class="tab-content">
        <div class="ai-insights">
          <h4><i class="fas fa-brain"></i> AI-Powered Insights</h4>
          <div id="aiInsights">
            <div class="insight-item">
              <strong>Readability Analysis:</strong> <span id="readabilityInsight">Analyzing...</span>
            </div>
            <div class="insight-item">
              <strong>Writing Style:</strong> <span id="styleInsight">Analyzing...</span>
            </div>
            <div class="insight-item">
              <strong>Recommendations:</strong> <span id="recommendationInsight">Analyzing...</span>
            </div>
          </div>
        </div>

        <div class="analytics-grid">
          <div class="chart-container">
            <h4>Sentence Length Distribution</h4>
            <canvas id="sentenceLengthChart" width="400" height="200"></canvas>
          </div>

          <div class="chart-container">
            <h4>Word Frequency Analysis</h4>
            <canvas id="wordFrequencyChart" width="400" height="200"></canvas>
          </div>

          <div class="chart-container">
            <h4>Readability Score</h4>
            <div class="readability-score">
              <div class="score-circle score-excellent" id="readabilityCircle">
                <span id="readabilityScore">85</span>
              </div>
              <div id="readabilityLevel">Excellent</div>
              <div class="progress-bar">
                <div class="progress-fill" id="readabilityProgress"></div>
              </div>
            </div>
          </div>

          <div class="chart-container">
            <h4>Text Complexity Metrics</h4>
            <canvas id="complexityChart" width="400" height="200"></canvas>
          </div>
        </div>
      </div>

      <!-- Export Tab -->
      <div id="export-tab" class="tab-content">
        <div class="input-section">
          <h3><i class="fas fa-download"></i> Advanced Export Options</h3>

          <div class="export-options">
            <div class="export-card" onclick="exportToPDF()">
              <i class="fas fa-file-pdf"></i>
              <h4>PDF Report</h4>
              <p>Professional analysis report with charts</p>
            </div>

            <div class="export-card" onclick="exportToWord()">
              <i class="fas fa-file-word"></i>
              <h4>Word Document</h4>
              <p>Formatted document with highlights</p>
            </div>

            <div class="export-card" onclick="exportToHTML()">
              <i class="fas fa-code"></i>
              <h4>HTML Page</h4>
              <p>Interactive web page with styling</p>
            </div>

            <div class="export-card" onclick="exportToJSON()">
              <i class="fas fa-database"></i>
              <h4>JSON Data</h4>
              <p>Structured data for developers</p>
            </div>

            <div class="export-card" onclick="exportToCSV()">
              <i class="fas fa-table"></i>
              <h4>CSV Spreadsheet</h4>
              <p>Sentence analysis in tabular format</p>
            </div>

            <div class="export-card" onclick="shareToCloud()">
              <i class="fas fa-cloud"></i>
              <h4>Cloud Share</h4>
              <p>Share via Google Drive, Dropbox</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Collaboration Tab -->
      <div id="collaboration-tab" class="tab-content">
        <div class="collaboration-panel">
          <h3><i class="fas fa-users"></i> Team Collaboration</h3>

          <div class="button-grid">
            <button type="button" class="btn btn-primary" onclick="generateShareLink()">
              <i class="fas fa-link"></i> Generate Share Link
            </button>
            <button type="button" class="btn btn-secondary" onclick="inviteCollaborators()">
              <i class="fas fa-user-plus"></i> Invite Team
            </button>
            <button type="button" class="btn btn-success" onclick="saveVersion()">
              <i class="fas fa-save"></i> Save Version
            </button>
          </div>

          <h4>Version History</h4>
          <div class="version-history" id="versionHistory">
            <div class="version-item">
              <span>Version 1.0 - Initial draft</span>
              <small>2 hours ago</small>
            </div>
            <div class="version-item">
              <span>Version 1.1 - Added introduction</span>
              <small>1 hour ago</small>
            </div>
            <div class="version-item">
              <span>Version 1.2 - Current version</span>
              <small>Just now</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Tab -->
      <div id="settings-tab" class="tab-content">
        <div class="input-section">
          <h3><i class="fas fa-cog"></i> Advanced Settings</h3>

          <div class="stats-grid">
            <div class="stat-card">
              <label>
                <input type="checkbox" id="autoSave" checked> Auto-save text
              </label>
            </div>
            <div class="stat-card">
              <label>
                <input type="checkbox" id="realTimeAnalysis" checked> Real-time analysis
              </label>
            </div>
            <div class="stat-card">
              <label>
                <input type="checkbox" id="showLineNumbers"> Show line numbers
              </label>
            </div>
            <div class="stat-card">
              <label>
                <input type="checkbox" id="highlightLongSentences" checked> Highlight long sentences
              </label>
            </div>
          </div>

          <h4>Analysis Preferences</h4>
          <div class="stats-grid">
            <div class="stat-card">
              <label>Reading Speed (WPM)</label>
              <input type="range" id="readingSpeed" min="150" max="300" value="225">
              <span id="readingSpeedValue">225</span>
            </div>
            <div class="stat-card">
              <label>Sentence Length Threshold</label>
              <input type="range" id="sentenceThreshold" min="15" max="30" value="20">
              <span id="sentenceThresholdValue">20</span>
            </div>
          </div>
        </div>
      </div>

      <aside class="sidebar">
        <h3><i class="fas fa-chart-bar"></i> Statistics</h3>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value" id="sentenceCount">0</div>
            <div class="stat-label">Sentences</div>
          </div>
          <div class="stat-card">
            <div class="stat-value" id="wordCount">0</div>
            <div class="stat-label">Words</div>
          </div>
          <div class="stat-card">
            <div class="stat-value" id="charCount">0</div>
            <div class="stat-label">Characters</div>
          </div>
          <div class="stat-card">
            <div class="stat-value" id="readingTime">0</div>
            <div class="stat-label">Min Read</div>
          </div>
          <div class="stat-card">
            <div class="stat-value" id="avgWordsPerSentence">0</div>
            <div class="stat-label">Avg Words/Sentence</div>
          </div>
        </div>

        <div class="keyboard-shortcuts">
          <h4>Keyboard Shortcuts</h4>
          <div class="shortcut">
            <span>Analyze Text</span>
            <span class="shortcut-key">Ctrl + Enter</span>
          </div>
          <div class="shortcut">
            <span>Copy Text</span>
            <span class="shortcut-key">Ctrl + C</span>
          </div>
          <div class="shortcut">
            <span>Clear Text</span>
            <span class="shortcut-key">Ctrl + Delete</span>
          </div>
          <div class="shortcut">
            <span>Toggle Theme</span>
            <span class="shortcut-key">Ctrl + D</span>
          </div>
        </div>
      </aside>
    </div>

    <div class="output-section">
      <div class="output-header">
        <h3><i class="fas fa-list"></i> Highlighted Sentences</h3>
        <div class="view-toggle">
          <button type="button" class="active" onclick="setViewMode('highlight')">Highlight View</button>
          <button type="button" onclick="setViewMode('list')">List View</button>
        </div>
      </div>
      <div id="output"></div>
    </div>

    <!-- Floating Action Toolbar -->
    <div class="floating-toolbar" id="floatingToolbar">
      <button type="button" class="floating-btn" onclick="quickAnalyze()" title="Quick Analyze">
        <i class="fas fa-bolt"></i>
      </button>
      <button type="button" class="floating-btn" onclick="openAIAssistant()" title="AI Assistant">
        <i class="fas fa-robot"></i>
      </button>
      <button type="button" class="floating-btn" onclick="toggleFullscreen()" title="Fullscreen">
        <i class="fas fa-expand"></i>
      </button>
      <button type="button" class="floating-btn" onclick="scrollToTop()" title="Scroll to Top">
        <i class="fas fa-arrow-up"></i>
      </button>
    </div>

    <!-- AI Assistant Modal -->
    <div class="modal" id="aiModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3><i class="fas fa-robot"></i> AI Writing Assistant</h3>
          <button type="button" class="close-modal" onclick="closeModal('aiModal')">&times;</button>
        </div>
        <div class="modal-body">
          <div class="ai-suggestions" id="aiSuggestions">
            <div class="insight-item">
              <strong>Grammar Check:</strong> <span id="grammarSuggestions">Analyzing...</span>
            </div>
            <div class="insight-item">
              <strong>Style Improvements:</strong> <span id="styleSuggestions">Analyzing...</span>
            </div>
            <div class="insight-item">
              <strong>Tone Analysis:</strong> <span id="toneSuggestions">Analyzing...</span>
            </div>
          </div>
          <div class="button-grid">
            <button type="button" class="btn btn-primary" onclick="applyAISuggestions()">
              <i class="fas fa-magic"></i> Apply Suggestions
            </button>
            <button type="button" class="btn btn-secondary" onclick="generateSummary()">
              <i class="fas fa-compress"></i> Generate Summary
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Share Modal -->
    <div class="modal" id="shareModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3><i class="fas fa-share"></i> Share Your Analysis</h3>
          <button type="button" class="close-modal" onclick="closeModal('shareModal')">&times;</button>
        </div>
        <div class="modal-body">
          <div class="share-options">
            <input type="text" id="shareLink" class="search-input" readonly placeholder="Share link will appear here...">
            <div class="button-grid">
              <button type="button" class="btn btn-primary" onclick="copyShareLink()">
                <i class="fas fa-copy"></i> Copy Link
              </button>
              <button type="button" class="btn btn-secondary" onclick="shareViaEmail()">
                <i class="fas fa-envelope"></i> Email
              </button>
              <button type="button" class="btn btn-success" onclick="shareToSocial('twitter')">
                <i class="fab fa-twitter"></i> Twitter
              </button>
              <button type="button" class="btn btn-outline" onclick="shareToSocial('linkedin')">
                <i class="fab fa-linkedin"></i> LinkedIn
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Enhanced color palette with better accessibility
    const colors = [
      '#ffadad', '#ffd6a5', '#fdffb6', '#caffbf',
      '#9bf6ff', '#a0c4ff', '#bdb2ff', '#ffc6ff',
      '#ffb3ba', '#ffdfba', '#ffffba', '#baffc9',
      '#bae1ff', '#c9c9ff', '#e6ccff', '#ffccf9'
    ];

    // Enhanced Application state
    let currentViewMode = 'highlight';
    let analysisData = null;
    let searchTerm = '';
    let currentTab = 'editor';
    let undoStack = [];
    let redoStack = [];
    let versionHistory = [];
    let currentLanguage = 'en';
    let aiInsights = {};
    let charts = {};
    let collaborationData = {
      shareLinks: [],
      collaborators: [],
      comments: []
    };

    // Advanced settings
    let settings = {
      autoSave: true,
      realTimeAnalysis: true,
      showLineNumbers: false,
      highlightLongSentences: true,
      readingSpeed: 225,
      sentenceThreshold: 20,
      aiAssistance: true,
      darkMode: false
    };

    // Initialize the enhanced application
    document.addEventListener('DOMContentLoaded', function() {
      initializeApp();
      setupEventListeners();
      initializeCharts();
      loadSampleText();
      showFloatingToolbar();
    });

    function initializeApp() {
      // Load saved settings
      const savedSettings = localStorage.getItem('sentenceHighlighterSettings');
      if (savedSettings) {
        settings = { ...settings, ...JSON.parse(savedSettings) };
      }

      // Apply saved theme
      if (settings.darkMode) {
        document.body.classList.add('dark-mode');
        updateThemeButton(true);
      }

      // Load saved text if available
      const savedText = localStorage.getItem('savedText');
      if (savedText) {
        document.getElementById('inputText').value = savedText;
        updateCharCounter();
        addToUndoStack(savedText);
      }

      // Load version history
      const savedVersions = localStorage.getItem('versionHistory');
      if (savedVersions) {
        versionHistory = JSON.parse(savedVersions);
        updateVersionHistory();
      }

      // Apply settings to UI
      applySettingsToUI();
    }

    function setupEventListeners() {
      const inputText = document.getElementById('inputText');
      const searchInput = document.getElementById('searchInput');

      // Text input events
      inputText.addEventListener('input', function() {
        updateCharCounter();
        saveTextToStorage();
        if (this.value.trim()) {
          debounce(highlightText, 500)();
        } else {
          clearOutput();
        }
      });

      // Search functionality
      searchInput.addEventListener('input', function() {
        searchTerm = this.value.toLowerCase();
        if (analysisData) {
          renderOutput();
        }
      });

      // Keyboard shortcuts
      document.addEventListener('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
          switch(e.key) {
            case 'Enter':
              e.preventDefault();
              highlightText();
              break;
            case 'c':
              if (e.target !== inputText) {
                e.preventDefault();
                copyFormatted();
              }
              break;
            case 'Delete':
              e.preventDefault();
              clearText();
              break;
            case 'd':
              e.preventDefault();
              toggleDarkMode();
              break;
          }
        }
      });
    }

    function loadSampleText() {
      const sampleText = `Welcome to the Advanced Sentence Highlighter Pro! This tool helps you analyze and visualize text structure. Each sentence is highlighted with a different color for easy identification. You can copy the formatted text, download it as a file, or search within your content. The tool also provides detailed statistics about your text including word count, reading time, and sentence complexity. Try pasting your own text to see how it works!`;

      if (!document.getElementById('inputText').value.trim()) {
        document.getElementById('inputText').value = sampleText;
        updateCharCounter();
        setTimeout(() => highlightText(), 100);
      }
    }

    function updateCharCounter() {
      const text = document.getElementById('inputText').value;
      const charCount = text.length;
      const charCounter = document.getElementById('charCounter');
      charCounter.textContent = `${charCount.toLocaleString()} characters`;

      // Update character count in stats
      document.getElementById('charCount').textContent = charCount.toLocaleString();
    }

    function saveTextToStorage() {
      const text = document.getElementById('inputText').value;
      localStorage.setItem('savedText', text);
    }

    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    function showLoading() {
      const output = document.getElementById('output');
      output.innerHTML = '<div class="loading"><i class="fas fa-spinner"></i> Analyzing text...</div>';
    }

    function clearOutput() {
      document.getElementById('output').innerHTML = '';
      updateStats(0, 0, 0, 0, 0);
    }

    function updateStats(sentences, words, chars, readingTime, avgWords) {
      document.getElementById('sentenceCount').textContent = sentences.toLocaleString();
      document.getElementById('wordCount').textContent = words.toLocaleString();
      document.getElementById('charCount').textContent = chars.toLocaleString();
      document.getElementById('readingTime').textContent = readingTime;
      document.getElementById('avgWordsPerSentence').textContent = avgWords;
    }

    function improvedSentenceSplit(text) {
      // Enhanced sentence splitting that handles abbreviations and edge cases
      const abbreviations = ['Dr', 'Mr', 'Mrs', 'Ms', 'Prof', 'Inc', 'Ltd', 'Co', 'Corp', 'etc', 'vs', 'e.g', 'i.e'];

      // Replace abbreviations temporarily
      let processedText = text;
      abbreviations.forEach(abbr => {
        const regex = new RegExp(`\\b${abbr}\\.`, 'gi');
        processedText = processedText.replace(regex, `${abbr}<!TEMP!>`);
      });

      // Split on sentence endings
      const sentences = processedText.split(/(?<=[.!?])\s+/)
        .map(sentence => sentence.replace(/<!TEMP!>/g, '.'))
        .filter(sentence => sentence.trim().length > 0);

      return sentences;
    }

    function calculateReadingTime(wordCount) {
      // Average reading speed: 200-250 words per minute
      const wordsPerMinute = 225;
      const minutes = Math.ceil(wordCount / wordsPerMinute);
      return minutes;
    }

    function getSentenceLengthCategory(wordCount) {
      if (wordCount <= 10) return 'short';
      if (wordCount <= 20) return 'medium';
      return 'long';
    }

    function highlightSearchTerm(text, term) {
      if (!term) return text;
      const regex = new RegExp(`(${term})`, 'gi');
      return text.replace(regex, '<span class="highlight-match">$1</span>');
    }

    function highlightText() {
      const text = document.getElementById('inputText').value.trim();
      if (!text) {
        clearOutput();
        return;
      }

      showLoading();

      // Simulate processing time for better UX
      setTimeout(() => {
        try {
          const sentences = improvedSentenceSplit(text);
          let totalWords = 0;

          const processedSentences = sentences.map((sentence, index) => {
            const words = sentence.split(/\s+/).filter(w => w.trim().length > 0);
            const wordCount = words.length;
            totalWords += wordCount;

            return {
              text: sentence.trim(),
              wordCount,
              index: index + 1,
              lengthCategory: getSentenceLengthCategory(wordCount),
              color: colors[index % colors.length]
            };
          });

          const readingTime = calculateReadingTime(totalWords);
          const avgWordsPerSentence = sentences.length > 0 ? Math.round(totalWords / sentences.length) : 0;

          analysisData = {
            sentences: processedSentences,
            totalSentences: sentences.length,
            totalWords,
            totalChars: text.length,
            readingTime,
            avgWordsPerSentence
          };

          updateStats(
            analysisData.totalSentences,
            analysisData.totalWords,
            analysisData.totalChars,
            analysisData.readingTime,
            analysisData.avgWordsPerSentence
          );

          renderOutput();
          showToast('Text analyzed successfully!', 'success');
        } catch (error) {
          console.error('Error analyzing text:', error);
          showToast('Error analyzing text. Please try again.', 'error');
        }
      }, 300);
    }

    function renderOutput() {
      const output = document.getElementById('output');
      if (!analysisData || analysisData.sentences.length === 0) {
        output.innerHTML = '<div class="loading">No sentences to display</div>';
        return;
      }

      let filteredSentences = analysisData.sentences;

      // Apply search filter
      if (searchTerm) {
        filteredSentences = analysisData.sentences.filter(sentence =>
          sentence.text.toLowerCase().includes(searchTerm)
        );
      }

      if (currentViewMode === 'highlight') {
        renderHighlightView(filteredSentences);
      } else {
        renderListView(filteredSentences);
      }
    }

    function renderHighlightView(sentences) {
      const output = document.getElementById('output');
      output.innerHTML = '';

      sentences.forEach(sentence => {
        const div = document.createElement('div');
        div.className = `sentence sentence-length-${sentence.lengthCategory}`;
        div.style.backgroundColor = sentence.color;
        div.style.borderLeftColor = getSentenceBorderColor(sentence.lengthCategory);

        const sentenceText = highlightSearchTerm(sentence.text, searchTerm);
        div.innerHTML = `
          <span class="sentence-number">#${sentence.index}</span>
          ${sentenceText}
        `;

        div.addEventListener('click', () => {
          showSentenceDetails(sentence);
        });

        output.appendChild(div);
      });

      if (sentences.length === 0 && searchTerm) {
        output.innerHTML = '<div class="loading">No sentences match your search</div>';
      }
    }

    function renderListView(sentences) {
      const output = document.getElementById('output');
      output.innerHTML = '';

      const list = document.createElement('div');
      list.style.fontFamily = 'monospace';
      list.style.fontSize = '14px';
      list.style.lineHeight = '1.8';

      sentences.forEach(sentence => {
        const div = document.createElement('div');
        div.style.padding = '0.5rem';
        div.style.borderBottom = '1px solid var(--border-light)';
        div.style.cursor = 'pointer';

        const sentenceText = highlightSearchTerm(sentence.text, searchTerm);
        div.innerHTML = `
          <strong>${sentence.index}.</strong> ${sentenceText}
          <span style="color: var(--text-light-secondary); font-size: 12px;">
            (${sentence.wordCount} words, ${sentence.lengthCategory})
          </span>
        `;

        div.addEventListener('click', () => {
          showSentenceDetails(sentence);
        });

        list.appendChild(div);
      });

      output.appendChild(list);

      if (sentences.length === 0 && searchTerm) {
        output.innerHTML = '<div class="loading">No sentences match your search</div>';
      }
    }

    function getSentenceBorderColor(category) {
      switch(category) {
        case 'short': return 'var(--success-color)';
        case 'medium': return 'var(--warning-color)';
        case 'long': return 'var(--danger-color)';
        default: return 'var(--border-light)';
      }
    }

    function showSentenceDetails(sentence) {
      const details = `
Sentence #${sentence.index}
Length: ${sentence.wordCount} words (${sentence.lengthCategory})
Text: "${sentence.text}"
      `;
      showToast(details, 'info');
    }

    function setViewMode(mode) {
      currentViewMode = mode;

      // Update button states
      document.querySelectorAll('.view-toggle button').forEach(btn => {
        btn.classList.remove('active');
      });
      event.target.classList.add('active');

      if (analysisData) {
        renderOutput();
      }
    }

    function copyFormatted() {
      const text = document.getElementById('inputText').value.trim();
      if (!text) {
        showToast('No text to copy', 'error');
        return;
      }

      try {
        const sentences = improvedSentenceSplit(text);
        const formatted = sentences.map((sentence, index) =>
          `${index + 1}. ${sentence.trim()}`
        ).join('\n\n');

        navigator.clipboard.writeText(formatted).then(() => {
          showToast('Text copied to clipboard!', 'success');
        }).catch(() => {
          // Fallback for older browsers
          const textArea = document.createElement('textarea');
          textArea.value = formatted;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          showToast('Text copied to clipboard!', 'success');
        });
      } catch (error) {
        showToast('Error copying text', 'error');
      }
    }

    function downloadText() {
      const text = document.getElementById('inputText').value.trim();
      if (!text) {
        showToast('No text to download', 'error');
        return;
      }

      try {
        const sentences = improvedSentenceSplit(text);
        const formatted = sentences.map((sentence, index) =>
          `${index + 1}. ${sentence.trim()}`
        ).join('\n\n');

        // Add metadata
        const metadata = `
Text Analysis Report
Generated: ${new Date().toLocaleString()}
Total Sentences: ${sentences.length}
Total Words: ${analysisData ? analysisData.totalWords : 'N/A'}
Reading Time: ${analysisData ? analysisData.readingTime : 'N/A'} minutes

---

${formatted}
        `;

        const blob = new Blob([metadata], { type: 'text/plain' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `sentence-analysis-${new Date().toISOString().split('T')[0]}.txt`;
        link.click();

        showToast('File downloaded successfully!', 'success');
      } catch (error) {
        showToast('Error downloading file', 'error');
      }
    }

    function loadFile(event) {
      const file = event.target.files[0];
      if (!file) return;

      if (file.size > 1024 * 1024) { // 1MB limit
        showToast('File too large. Please select a file under 1MB.', 'error');
        return;
      }

      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          document.getElementById('inputText').value = e.target.result;
          updateCharCounter();
          highlightText();
          showToast('File loaded successfully!', 'success');
        } catch (error) {
          showToast('Error reading file', 'error');
        }
      };

      reader.onerror = function() {
        showToast('Error reading file', 'error');
      };

      reader.readAsText(file);
    }

    function clearText() {
      if (confirm('Are you sure you want to clear all text?')) {
        document.getElementById('inputText').value = '';
        document.getElementById('searchInput').value = '';
        searchTerm = '';
        analysisData = null;
        clearOutput();
        updateCharCounter();
        localStorage.removeItem('savedText');
        showToast('Text cleared', 'success');
      }
    }

    function toggleDarkMode() {
      const isDark = document.body.classList.toggle('dark-mode');
      updateThemeButton(isDark);
      localStorage.setItem('theme', isDark ? 'dark' : 'light');
      showToast(`${isDark ? 'Dark' : 'Light'} mode enabled`, 'success');
    }

    function updateThemeButton(isDark) {
      const icon = document.getElementById('themeIcon');
      const text = document.getElementById('themeText');

      if (isDark) {
        icon.className = 'fas fa-sun';
        text.textContent = 'Light Mode';
      } else {
        icon.className = 'fas fa-moon';
        text.textContent = 'Dark Mode';
      }
    }

    function showToast(message, type = 'success') {
      // Remove existing toast
      const existingToast = document.querySelector('.toast');
      if (existingToast) {
        existingToast.remove();
      }

      const toast = document.createElement('div');
      toast.className = `toast ${type}`;
      toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'}"></i>
        ${message}
      `;

      document.body.appendChild(toast);

      // Show toast
      setTimeout(() => toast.classList.add('show'), 100);

      // Hide toast after 3 seconds
      setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }

    // ===== ADVANCED FEATURES =====

    // Tab Management
    function switchTab(tabName) {
      // Hide all tab contents
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
      });

      // Remove active class from all tabs
      document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
      });

      // Show selected tab content
      document.getElementById(`${tabName}-tab`).classList.add('active');

      // Add active class to selected tab
      event.target.classList.add('active');

      currentTab = tabName;

      // Load tab-specific data
      if (tabName === 'analytics' && analysisData) {
        updateAnalytics();
      }
    }

    // Undo/Redo Functionality
    function addToUndoStack(text) {
      undoStack.push(text);
      if (undoStack.length > 50) { // Limit stack size
        undoStack.shift();
      }
      redoStack = []; // Clear redo stack when new action is performed
    }

    function undoAction() {
      if (undoStack.length > 1) {
        const currentText = undoStack.pop();
        redoStack.push(currentText);
        const previousText = undoStack[undoStack.length - 1];
        document.getElementById('inputText').value = previousText;
        updateCharCounter();
        if (settings.realTimeAnalysis) {
          debounce(highlightText, 300)();
        }
        showToast('Undo successful', 'success');
      } else {
        showToast('Nothing to undo', 'error');
      }
    }

    function redoAction() {
      if (redoStack.length > 0) {
        const redoText = redoStack.pop();
        undoStack.push(redoText);
        document.getElementById('inputText').value = redoText;
        updateCharCounter();
        if (settings.realTimeAnalysis) {
          debounce(highlightText, 300)();
        }
        showToast('Redo successful', 'success');
      } else {
        showToast('Nothing to redo', 'error');
      }
    }

    // Language Support
    function changeLanguage() {
      currentLanguage = document.getElementById('languageSelect').value;
      if (analysisData) {
        highlightText(); // Re-analyze with new language
      }
      showToast(`Language changed to ${getLanguageName(currentLanguage)}`, 'success');
    }

    function getLanguageName(code) {
      const languages = {
        'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
        'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'zh': 'Chinese',
        'ja': 'Japanese', 'ar': 'Arabic'
      };
      return languages[code] || 'Unknown';
    }

    // Advanced Analytics
    function updateAnalytics() {
      if (!analysisData) return;

      generateAIInsights();
      updateCharts();
      calculateReadabilityScore();
    }

    function generateAIInsights() {
      const insights = {
        readability: analyzeReadability(),
        style: analyzeWritingStyle(),
        recommendations: generateRecommendations()
      };

      document.getElementById('readabilityInsight').textContent = insights.readability;
      document.getElementById('styleInsight').textContent = insights.style;
      document.getElementById('recommendationInsight').textContent = insights.recommendations;
    }

    function analyzeReadability() {
      if (!analysisData) return 'No text to analyze';

      const avgWordsPerSentence = analysisData.avgWordsPerSentence;
      const totalSentences = analysisData.totalSentences;

      if (avgWordsPerSentence < 15) {
        return 'Very easy to read - short, clear sentences';
      } else if (avgWordsPerSentence < 20) {
        return 'Easy to read - good sentence length balance';
      } else if (avgWordsPerSentence < 25) {
        return 'Moderate difficulty - consider shorter sentences';
      } else {
        return 'Difficult to read - sentences are too long';
      }
    }

    function analyzeWritingStyle() {
      if (!analysisData) return 'No text to analyze';

      const sentences = analysisData.sentences;
      const shortSentences = sentences.filter(s => s.wordCount <= 10).length;
      const longSentences = sentences.filter(s => s.wordCount > 20).length;
      const ratio = shortSentences / sentences.length;

      if (ratio > 0.6) {
        return 'Concise and punchy - great for engagement';
      } else if (ratio > 0.3) {
        return 'Balanced mix of sentence lengths - good flow';
      } else {
        return 'Complex and detailed - consider varying sentence length';
      }
    }

    function generateRecommendations() {
      if (!analysisData) return 'No text to analyze';

      const recommendations = [];

      if (analysisData.avgWordsPerSentence > 25) {
        recommendations.push('Break down long sentences');
      }

      if (analysisData.totalSentences < 3) {
        recommendations.push('Add more content for better analysis');
      }

      const longSentenceCount = analysisData.sentences.filter(s => s.wordCount > 30).length;
      if (longSentenceCount > analysisData.totalSentences * 0.3) {
        recommendations.push('Reduce complex sentence structures');
      }

      return recommendations.length > 0 ? recommendations.join(', ') : 'Great writing! No major issues detected.';
    }

    function calculateReadabilityScore() {
      if (!analysisData) return;

      // Simplified Flesch Reading Ease calculation
      const avgWordsPerSentence = analysisData.avgWordsPerSentence;
      const avgSyllablesPerWord = estimateAvgSyllables();

      const score = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);
      const clampedScore = Math.max(0, Math.min(100, Math.round(score)));

      document.getElementById('readabilityScore').textContent = clampedScore;
      document.getElementById('readabilityProgress').style.width = `${clampedScore}%`;

      let level, className;
      if (clampedScore >= 80) {
        level = 'Excellent';
        className = 'score-excellent';
      } else if (clampedScore >= 60) {
        level = 'Good';
        className = 'score-good';
      } else {
        level = 'Needs Improvement';
        className = 'score-fair';
      }

      document.getElementById('readabilityLevel').textContent = level;
      const circle = document.getElementById('readabilityCircle');
      circle.className = `score-circle ${className}`;
    }

    function estimateAvgSyllables() {
      if (!analysisData) return 1;

      let totalSyllables = 0;
      let totalWords = 0;

      analysisData.sentences.forEach(sentence => {
        const words = sentence.text.split(/\s+/).filter(w => w.trim().length > 0);
        words.forEach(word => {
          totalSyllables += countSyllables(word);
          totalWords++;
        });
      });

      return totalWords > 0 ? totalSyllables / totalWords : 1;
    }

    function countSyllables(word) {
      word = word.toLowerCase();
      if (word.length <= 3) return 1;
      word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '');
      word = word.replace(/^y/, '');
      const matches = word.match(/[aeiouy]{1,2}/g);
      return matches ? matches.length : 1;
    }

    // Chart Initialization and Updates
    function initializeCharts() {
      // Initialize Chart.js charts
      const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: true,
            position: 'top'
          }
        }
      };

      // Sentence Length Chart
      const sentenceLengthCtx = document.getElementById('sentenceLengthChart').getContext('2d');
      charts.sentenceLength = new Chart(sentenceLengthCtx, {
        type: 'bar',
        data: {
          labels: ['Short (≤10)', 'Medium (11-20)', 'Long (21-30)', 'Very Long (>30)'],
          datasets: [{
            label: 'Number of Sentences',
            data: [0, 0, 0, 0],
            backgroundColor: ['#10b981', '#f59e0b', '#ef4444', '#8b5cf6']
          }]
        },
        options: chartOptions
      });

      // Word Frequency Chart
      const wordFrequencyCtx = document.getElementById('wordFrequencyChart').getContext('2d');
      charts.wordFrequency = new Chart(wordFrequencyCtx, {
        type: 'doughnut',
        data: {
          labels: [],
          datasets: [{
            data: [],
            backgroundColor: colors.slice(0, 8)
          }]
        },
        options: chartOptions
      });

      // Complexity Chart
      const complexityCtx = document.getElementById('complexityChart').getContext('2d');
      charts.complexity = new Chart(complexityCtx, {
        type: 'radar',
        data: {
          labels: ['Readability', 'Sentence Variety', 'Word Complexity', 'Structure', 'Flow'],
          datasets: [{
            label: 'Text Metrics',
            data: [0, 0, 0, 0, 0],
            backgroundColor: 'rgba(79, 70, 229, 0.2)',
            borderColor: 'rgba(79, 70, 229, 1)',
            pointBackgroundColor: 'rgba(79, 70, 229, 1)'
          }]
        },
        options: {
          ...chartOptions,
          scales: {
            r: {
              beginAtZero: true,
              max: 100
            }
          }
        }
      });
    }

    function updateCharts() {
      if (!analysisData) return;

      updateSentenceLengthChart();
      updateWordFrequencyChart();
      updateComplexityChart();
    }

    function updateSentenceLengthChart() {
      const sentences = analysisData.sentences;
      const counts = [0, 0, 0, 0]; // short, medium, long, very long

      sentences.forEach(sentence => {
        if (sentence.wordCount <= 10) counts[0]++;
        else if (sentence.wordCount <= 20) counts[1]++;
        else if (sentence.wordCount <= 30) counts[2]++;
        else counts[3]++;
      });

      charts.sentenceLength.data.datasets[0].data = counts;
      charts.sentenceLength.update();
    }

    function updateWordFrequencyChart() {
      const wordFreq = calculateWordFrequency();
      const topWords = Object.entries(wordFreq)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 8);

      charts.wordFrequency.data.labels = topWords.map(([word]) => word);
      charts.wordFrequency.data.datasets[0].data = topWords.map(([,count]) => count);
      charts.wordFrequency.update();
    }

    function calculateWordFrequency() {
      const frequency = {};
      const commonWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they']);

      analysisData.sentences.forEach(sentence => {
        const words = sentence.text.toLowerCase().match(/\b\w+\b/g) || [];
        words.forEach(word => {
          if (word.length > 3 && !commonWords.has(word)) {
            frequency[word] = (frequency[word] || 0) + 1;
          }
        });
      });

      return frequency;
    }

    function updateComplexityChart() {
      const metrics = calculateComplexityMetrics();
      charts.complexity.data.datasets[0].data = [
        metrics.readability,
        metrics.sentenceVariety,
        metrics.wordComplexity,
        metrics.structure,
        metrics.flow
      ];
      charts.complexity.update();
    }

    function calculateComplexityMetrics() {
      const sentences = analysisData.sentences;
      const avgWordLength = calculateAvgWordLength();
      const sentenceVariety = calculateSentenceVariety();

      return {
        readability: Math.min(100, (30 - analysisData.avgWordsPerSentence) * 4),
        sentenceVariety: sentenceVariety,
        wordComplexity: Math.min(100, (avgWordLength - 3) * 20),
        structure: calculateStructureScore(),
        flow: calculateFlowScore()
      };
    }

    function calculateAvgWordLength() {
      let totalLength = 0;
      let wordCount = 0;

      analysisData.sentences.forEach(sentence => {
        const words = sentence.text.match(/\b\w+\b/g) || [];
        words.forEach(word => {
          totalLength += word.length;
          wordCount++;
        });
      });

      return wordCount > 0 ? totalLength / wordCount : 0;
    }

    function calculateSentenceVariety() {
      const lengths = analysisData.sentences.map(s => s.wordCount);
      const variance = calculateVariance(lengths);
      return Math.min(100, variance * 2);
    }

    function calculateVariance(numbers) {
      const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
      const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
      return squaredDiffs.reduce((a, b) => a + b, 0) / numbers.length;
    }

    function calculateStructureScore() {
      // Simple heuristic based on sentence count and distribution
      const sentenceCount = analysisData.totalSentences;
      if (sentenceCount < 3) return 30;
      if (sentenceCount < 10) return 60;
      return 85;
    }

    function calculateFlowScore() {
      // Analyze transition words and sentence connections
      const transitionWords = ['however', 'therefore', 'moreover', 'furthermore', 'consequently', 'meanwhile', 'additionally', 'similarly', 'conversely', 'nonetheless'];
      let transitionCount = 0;

      analysisData.sentences.forEach(sentence => {
        const text = sentence.text.toLowerCase();
        transitionWords.forEach(word => {
          if (text.includes(word)) transitionCount++;
        });
      });

      const transitionRatio = transitionCount / analysisData.totalSentences;
      return Math.min(100, transitionRatio * 200 + 40);
    }

    // Advanced Export Functions
    function exportToPDF() {
      if (!analysisData) {
        showToast('No analysis data to export', 'error');
        return;
      }

      try {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Add title
        doc.setFontSize(20);
        doc.text('Sentence Analysis Report', 20, 30);

        // Add metadata
        doc.setFontSize(12);
        doc.text(`Generated: ${new Date().toLocaleString()}`, 20, 50);
        doc.text(`Total Sentences: ${analysisData.totalSentences}`, 20, 60);
        doc.text(`Total Words: ${analysisData.totalWords}`, 20, 70);
        doc.text(`Reading Time: ${analysisData.readingTime} minutes`, 20, 80);

        // Add sentences
        let yPosition = 100;
        analysisData.sentences.forEach((sentence, index) => {
          if (yPosition > 250) {
            doc.addPage();
            yPosition = 30;
          }
          doc.text(`${index + 1}. ${sentence.text}`, 20, yPosition);
          yPosition += 10;
        });

        doc.save(`sentence-analysis-${new Date().toISOString().split('T')[0]}.pdf`);
        showToast('PDF exported successfully!', 'success');
      } catch (error) {
        showToast('Error exporting PDF', 'error');
      }
    }

    function exportToHTML() {
      if (!analysisData) {
        showToast('No analysis data to export', 'error');
        return;
      }

      const htmlContent = generateHTMLReport();
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `sentence-analysis-${new Date().toISOString().split('T')[0]}.html`;
      link.click();
      showToast('HTML exported successfully!', 'success');
    }

    function generateHTMLReport() {
      return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentence Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { background: #4f46e5; color: white; padding: 20px; border-radius: 8px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: #f8fafc; padding: 15px; border-radius: 8px; text-align: center; }
        .sentence { margin: 10px 0; padding: 10px; border-left: 4px solid #4f46e5; background: #f8fafc; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Sentence Analysis Report</h1>
        <p>Generated on ${new Date().toLocaleString()}</p>
    </div>

    <div class="stats">
        <div class="stat-card">
            <h3>${analysisData.totalSentences}</h3>
            <p>Total Sentences</p>
        </div>
        <div class="stat-card">
            <h3>${analysisData.totalWords}</h3>
            <p>Total Words</p>
        </div>
        <div class="stat-card">
            <h3>${analysisData.readingTime}</h3>
            <p>Reading Time (min)</p>
        </div>
        <div class="stat-card">
            <h3>${analysisData.avgWordsPerSentence}</h3>
            <p>Avg Words/Sentence</p>
        </div>
    </div>

    <h2>Analyzed Sentences</h2>
    ${analysisData.sentences.map((sentence, index) =>
      `<div class="sentence">
         <strong>${index + 1}.</strong> ${sentence.text}
         <small style="color: #666; display: block; margin-top: 5px;">
           ${sentence.wordCount} words | ${sentence.lengthCategory} length
         </small>
       </div>`
    ).join('')}
</body>
</html>`;
    }

    function exportToJSON() {
      if (!analysisData) {
        showToast('No analysis data to export', 'error');
        return;
      }

      const exportData = {
        metadata: {
          exportDate: new Date().toISOString(),
          language: currentLanguage,
          version: '2.0'
        },
        analysis: analysisData,
        insights: aiInsights,
        settings: settings
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `sentence-analysis-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      showToast('JSON exported successfully!', 'success');
    }

    function exportToCSV() {
      if (!analysisData) {
        showToast('No analysis data to export', 'error');
        return;
      }

      const csvContent = [
        ['Sentence Number', 'Text', 'Word Count', 'Length Category', 'Character Count'],
        ...analysisData.sentences.map((sentence, index) => [
          index + 1,
          `"${sentence.text.replace(/"/g, '""')}"`,
          sentence.wordCount,
          sentence.lengthCategory,
          sentence.text.length
        ])
      ].map(row => row.join(',')).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `sentence-analysis-${new Date().toISOString().split('T')[0]}.csv`;
      link.click();
      showToast('CSV exported successfully!', 'success');
    }

    function exportToWord() {
      showToast('Word export feature coming soon!', 'info');
    }

    function shareToCloud() {
      showToast('Cloud sharing feature coming soon!', 'info');
    }

    // Collaboration Features
    function generateShareLink() {
      const shareData = {
        text: document.getElementById('inputText').value,
        analysis: analysisData,
        timestamp: Date.now()
      };

      const shareId = btoa(JSON.stringify(shareData)).substring(0, 16);
      const shareLink = `${window.location.origin}${window.location.pathname}?share=${shareId}`;

      document.getElementById('shareLink').value = shareLink;
      collaborationData.shareLinks.push({
        id: shareId,
        link: shareLink,
        created: new Date().toISOString()
      });

      document.getElementById('shareModal').classList.add('show');
      showToast('Share link generated!', 'success');
    }

    function copyShareLink() {
      const shareLink = document.getElementById('shareLink').value;
      if (shareLink) {
        navigator.clipboard.writeText(shareLink).then(() => {
          showToast('Share link copied to clipboard!', 'success');
        });
      }
    }

    function inviteCollaborators() {
      showToast('Team collaboration feature coming soon!', 'info');
    }

    function saveVersion() {
      const version = {
        id: Date.now(),
        text: document.getElementById('inputText').value,
        analysis: analysisData,
        timestamp: new Date().toISOString(),
        name: `Version ${versionHistory.length + 1}`
      };

      versionHistory.push(version);
      localStorage.setItem('versionHistory', JSON.stringify(versionHistory));
      updateVersionHistory();
      showToast('Version saved successfully!', 'success');
    }

    function updateVersionHistory() {
      const historyContainer = document.getElementById('versionHistory');
      historyContainer.innerHTML = versionHistory.slice(-10).reverse().map(version => `
        <div class="version-item">
          <span>${version.name}</span>
          <small>${new Date(version.timestamp).toLocaleString()}</small>
          <button type="button" class="btn btn-outline" onclick="loadVersion(${version.id})" style="margin-left: auto; padding: 0.25rem 0.5rem; font-size: 0.75rem;">
            Load
          </button>
        </div>
      `).join('');
    }

    function loadVersion(versionId) {
      const version = versionHistory.find(v => v.id === versionId);
      if (version) {
        document.getElementById('inputText').value = version.text;
        updateCharCounter();
        highlightText();
        showToast('Version loaded successfully!', 'success');
      }
    }

    // AI Assistant Features
    function openAIAssistant() {
      if (!analysisData) {
        showToast('Please analyze text first', 'error');
        return;
      }

      generateAISuggestions();
      document.getElementById('aiModal').classList.add('show');
    }

    function generateAISuggestions() {
      // Simulate AI analysis
      setTimeout(() => {
        document.getElementById('grammarSuggestions').textContent = 'No major grammar issues detected. Consider checking punctuation in longer sentences.';
        document.getElementById('styleSuggestions').textContent = 'Writing style is clear and engaging. Consider varying sentence beginnings for better flow.';
        document.getElementById('toneSuggestions').textContent = 'Tone is professional and informative. Suitable for the intended audience.';
      }, 1000);
    }

    function applyAISuggestions() {
      showToast('AI suggestions applied!', 'success');
      closeModal('aiModal');
    }

    function generateSummary() {
      if (!analysisData) return;

      const sentences = analysisData.sentences;
      const summary = sentences.slice(0, Math.min(3, Math.ceil(sentences.length * 0.3)))
        .map(s => s.text).join(' ');

      navigator.clipboard.writeText(summary).then(() => {
        showToast('Summary copied to clipboard!', 'success');
      });
    }

    // Floating Toolbar Functions
    function showFloatingToolbar() {
      setTimeout(() => {
        document.getElementById('floatingToolbar').classList.add('show');
      }, 2000);
    }

    function quickAnalyze() {
      highlightText();
      showToast('Quick analysis complete!', 'success');
    }

    function toggleFullscreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
        showToast('Entered fullscreen mode', 'success');
      } else {
        document.exitFullscreen();
        showToast('Exited fullscreen mode', 'success');
      }
    }

    function scrollToTop() {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // Modal Management
    function closeModal(modalId) {
      document.getElementById(modalId).classList.remove('show');
    }

    // Settings Management
    function applySettingsToUI() {
      document.getElementById('autoSave').checked = settings.autoSave;
      document.getElementById('realTimeAnalysis').checked = settings.realTimeAnalysis;
      document.getElementById('showLineNumbers').checked = settings.showLineNumbers;
      document.getElementById('highlightLongSentences').checked = settings.highlightLongSentences;
      document.getElementById('readingSpeed').value = settings.readingSpeed;
      document.getElementById('sentenceThreshold').value = settings.sentenceThreshold;

      // Update display values
      document.getElementById('readingSpeedValue').textContent = settings.readingSpeed;
      document.getElementById('sentenceThresholdValue').textContent = settings.sentenceThreshold;
    }

    // Settings Event Listeners
    document.addEventListener('DOMContentLoaded', function() {
      // Reading speed slider
      const readingSpeedSlider = document.getElementById('readingSpeed');
      if (readingSpeedSlider) {
        readingSpeedSlider.addEventListener('input', function() {
          settings.readingSpeed = parseInt(this.value);
          document.getElementById('readingSpeedValue').textContent = this.value;
          saveSettings();
          if (analysisData) {
            analysisData.readingTime = calculateReadingTime(analysisData.totalWords);
            updateStats(analysisData.totalSentences, analysisData.totalWords, analysisData.totalChars, analysisData.readingTime, analysisData.avgWordsPerSentence);
          }
        });
      }

      // Sentence threshold slider
      const sentenceThresholdSlider = document.getElementById('sentenceThreshold');
      if (sentenceThresholdSlider) {
        sentenceThresholdSlider.addEventListener('input', function() {
          settings.sentenceThreshold = parseInt(this.value);
          document.getElementById('sentenceThresholdValue').textContent = this.value;
          saveSettings();
        });
      }

      // Checkbox settings
      ['autoSave', 'realTimeAnalysis', 'showLineNumbers', 'highlightLongSentences'].forEach(settingId => {
        const checkbox = document.getElementById(settingId);
        if (checkbox) {
          checkbox.addEventListener('change', function() {
            settings[settingId] = this.checked;
            saveSettings();
            showToast(`${settingId} ${this.checked ? 'enabled' : 'disabled'}`, 'success');
          });
        }
      });
    });

    function saveSettings() {
      localStorage.setItem('sentenceHighlighterSettings', JSON.stringify(settings));
    }

    // Social Sharing
    function shareViaEmail() {
      const subject = 'Text Analysis Report';
      const body = `Check out this text analysis report: ${document.getElementById('shareLink').value}`;
      window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
    }

    function shareToSocial(platform) {
      const url = document.getElementById('shareLink').value;
      const text = 'Check out this amazing text analysis!';

      let shareUrl;
      if (platform === 'twitter') {
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
      } else if (platform === 'linkedin') {
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
      }

      if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
      }
    }

    // Enhanced Export functionality for potential future use
    window.SentenceHighlighterPro = {
      // Core functions
      highlightText,
      copyFormatted,
      downloadText,
      toggleDarkMode,
      clearText,

      // Advanced functions
      exportToPDF,
      exportToHTML,
      exportToJSON,
      exportToCSV,
      generateShareLink,
      saveVersion,
      openAIAssistant,

      // Data access
      getAnalysisData: () => analysisData,
      getSettings: () => settings,
      getVersionHistory: () => versionHistory
    };

    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('modal')) {
        e.target.classList.remove('show');
      }
    });
  </script>
</body>
</html>
