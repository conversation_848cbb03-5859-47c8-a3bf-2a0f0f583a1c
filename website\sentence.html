<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sentence Highlighter</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 20px;
      background: #f9f9f9;
    }

    textarea {
      width: 100%;
      height: 150px;
      padding: 10px;
      font-size: 16px;
      font-family: inherit;
      border: 1px solid #ccc;
      border-radius: 6px;
      margin-bottom: 20px;
      resize: vertical;
    }

    #output {
      font-size: 18px;
      line-height: 1.8;
      white-space: pre-wrap;
    }

    .sentence {
      display: block;
      padding: 5px 8px;
      margin-bottom: 4px;
      border-radius: 5px;
      font-weight: 500;
    }

    button {
      padding: 8px 15px;
      margin-right: 10px;
      border: none;
      border-radius: 4px;
      background-color: #007BFF;
      color: white;
      font-size: 16px;
      cursor: pointer;
    }

    button:hover {
      background-color: #0056b3;
    }

    .button-group {
      margin-bottom: 20px;
    }
  </style>
</head>
<body>

  <h2>Paste your text below:</h2>
  <textarea id="inputText" placeholder="Paste your text here..."></textarea>
  
  <div class="button-group">
    <button onclick="highlightText()">Highlight Sentences</button>
    <button onclick="copyToClipboard()">Copy Formatted Text</button>
  </div>

  <h3>Highlighted Output:</h3>
  <div id="output"></div>

  <script>
    const colors = ['#ffadad', '#ffd6a5', '#fdffb6', '#caffbf', '#9bf6ff', '#a0c4ff', '#bdb2ff', '#ffc6ff'];

    function highlightText() {
      const text = document.getElementById('inputText').value.trim();
      const output = document.getElementById('output');
      output.innerHTML = '';

      if (!text) {
        output.innerHTML = '<em>No text provided.</em>';
        return;
      }

      const sentences = text.split(/(?<=\.)\s+/);
      sentences.forEach((sentence, index) => {
        const span = document.createElement('span');
        span.className = 'sentence';
        span.style.backgroundColor = colors[index % colors.length];
        span.textContent = sentence;
        output.appendChild(span);
      });
    }

    function copyToClipboard() {
      const text = document.getElementById('inputText').value.trim();
      const sentences = text.split(/(?<=\.)\s+/);
      const formatted = sentences.join('\n');
      
      navigator.clipboard.writeText(formatted).then(() => {
        alert('Formatted text copied! You can now paste it into Word.');
      }, () => {
        alert('Copy failed. Please try again.');
      });
    }
  </script>

</body>
</html>
