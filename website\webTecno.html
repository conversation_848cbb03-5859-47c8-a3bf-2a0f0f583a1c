<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple Calculator</title>
  <link rel="stylesheet" href="style.css">  
  <style>
    body {
      background-color: #f0f8ff;
      font-family: Arial, sans-serif;
      text-align: center;
      padding: 20px;
    }
    
    h2 {
      color: #4b0082;
      text-shadow: 1px 1px 2px #ccc;
    }
    
    input {
      padding: 10px;
      margin: 5px;
      border: 2px solid #6a5acd;
      border-radius: 5px;
      width: 200px;
    }
    
    button {
      padding: 10px 15px;
      margin: 30px;
      border: 2px solid #6a5acd;
      border-radius: 5px;
      cursor: pointer;
      font-weight: bold;
      background-color: #6a5acd;
      color: white;
      width: 120px;
      font-size: 16px;
      text-transform: uppercase;
      font-family: 'Arial', sans-serif;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      transition: transform 0.2s, box-shadow 0.2s;
    }
    
    button:hover {
      transform: scale(1.05);
    }
    
    button:nth-child(1) { background-color: #ff6347; color: white; }
    button:nth-child(2) { background-color: #4169e1; color: white; }
    button:nth-child(3) { background-color: #32cd32; color: white; }
    button:nth-child(4) { background-color: #ffa500; color: white; }
    button:nth-child(5) { background-color: #808080; color: white; }
    
    #result {
      font-size: 24px;
      font-weight: bold;
      color: #8a2be2;
      margin-top: 20px;
    }
  </style>
</head>
<script>

  function add() {
   let a = parseFloat(document.getElementById("num1").value);
   let b = parseFloat(document.getElementById("num2").value);
   document.getElementById("result").innerText = "Result: " + (a + b);
}

function subtract() {
  let a = parseFloat(document.getElementById("num1").value);
  let b = parseFloat(document.getElementById("num2").value);
  document.getElementById("result").innerText = "Result: " + (a - b);
}

function clearFields() {
  document.getElementById("num1").value = "";
  document.getElementById("num2").value = "";
  document.getElementById("result").innerText = "";
}

function multiply() {
  let a = parseFloat(document.getElementById("num1").value);
  let b = parseFloat(document.getElementById("num2").value);
  document.getElementById("result").innerText = "Result: " + (a * b);
}

function divide() {
  let a = parseFloat(document.getElementById("num1").value);
  let b = parseFloat(document.getElementById("num2").value);
  document.getElementById("result").innerText = "Result: " + (a / b);
}

</script>
<body>

  <h2>Simple Calculator</h2><br>
  <input type="number" id="num1" placeholder="Enter number 1"><br>
  <input type="number" id="num2" placeholder="Enter number 2"><br><br>

  <button onclick="add()">Add</button>
  <button onclick="subtract()">Subtract</button>
  <button onclick="multiply()">Multiply</button>
  <button onclick="divide()">Divide</button>
  <button onclick="clearFields()">Clear</button>
  <br><br>
  <p id="result"></p>
  <!-- practice your skills with this simple calculator. You can add, subtract, multiply, and divide two numbers. Enjoy coding! -->
<!-- 
  <input type="number" id="num1" placeholder="Enter number 1">
  <button onclick="add()">Add  </button>
   -->



</body>
</html>




