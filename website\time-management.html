<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Futuristic Task Dashboard</title>
  <style>
    body {
      margin: 0;
      font-family: 'Orbitron', sans-serif;
      background: radial-gradient(circle at top left, #0f0f0f, #1a1a2e);
      color: #00ffe7;
    }

    header {
      text-align: center;
      padding: 1rem;
      border-bottom: 1px solid #00ffe7;
    }

    .container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      padding: 2rem;
    }

    .task-form, .task-list {
      background: rgba(0, 255, 231, 0.1);
      border: 1px solid #00ffe7;
      border-radius: 10px;
      padding: 1rem;
    }

    input, textarea, select, button {
      width: 100%;
      margin: 0.5rem 0;
      padding: 0.5rem;
      font-family: inherit;
      border: none;
      border-radius: 5px;
      background: #1a1a2e;
      color: #00ffe7;
    }

    .task {
      background: #0f0f0f;
      border: 1px solid #00ffe7;
      border-radius: 5px;
      margin: 0.5rem 0;
      padding: 0.5rem;
    }

    .progress {
      height: 10px;
      background: #333;
      border-radius: 5px;
      overflow: hidden;
      margin-top: 0.5rem;
    }

    .progress-bar {
      height: 100%;
      background: #00ffe7;
      transition: width 0.3s ease;
    }

    .time-info {
      font-size: 0.85rem;
      color: #88fff5;
    }
  </style>
</head>
<body>
  <header>
    <h1>Futuristic Task Management Dashboard</h1>
    <p>Track your productivity in a 24-hour window</p>
  </header>
  <main class="container">
    <section class="task-form">
      <h2>Add Task</h2>
      <input type="text" id="taskTitle" placeholder="Task Title">
      <input type="number" id="taskDuration" placeholder="Duration (minutes)">
      <textarea id="taskDescription" placeholder="Description"></textarea>
      <button onclick="addTask()">Add Task</button>
    </section>
    <section class="task-list">
      <h2>Today's Tasks</h2>
      <div id="tasksContainer"></div>
    </section>
  </main>

  <script>
    let tasks = [];

    function addTask() {
      const title = document.getElementById('taskTitle').value;
      const duration = parseInt(document.getElementById('taskDuration').value);
      const description = document.getElementById('taskDescription').value;

      if (!title || !duration) return;

      const task = {
        id: Date.now(),
        title,
        duration,
        wasted: 0,
        completed: false,
        description
      };

      tasks.push(task);
      renderTasks();
      document.getElementById('taskTitle').value = '';
      document.getElementById('taskDuration').value = '';
      document.getElementById('taskDescription').value = '';
    }

    function renderTasks() {
      const container = document.getElementById('tasksContainer');
      container.innerHTML = '';
      tasks.forEach(task => {
        const div = document.createElement('div');
        div.className = 'task';

        const used = task.duration - task.wasted;
        const percentUsed = Math.max((used / task.duration) * 100, 0);

        div.innerHTML = `
          <h3>${task.title}</h3>
          <p>${task.description}</p>
          <div class="time-info">Duration: ${task.duration} min | Wasted: ${task.wasted} min | Used: ${used} min</div>
          <div class="progress">
            <div class="progress-bar" style="width: ${percentUsed}%;"></div>
          </div>
          <button onclick="wasteTime(${task.id})">Add 5 Min Wasted</button>
          <button onclick="deleteTask(${task.id})">Delete</button>
        `;

        container.appendChild(div);
      });
    }

    function wasteTime(id) {
      const task = tasks.find(t => t.id === id);
      if (task) {
        task.wasted += 5;
        renderTasks();
      }
    }

    function deleteTask(id) {
      tasks = tasks.filter(t => t.id !== id);
      renderTasks();
    }
  </script>
</body>
</html>
