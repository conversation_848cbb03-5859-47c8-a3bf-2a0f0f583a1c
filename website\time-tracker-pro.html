<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TimeTracker Pro - Enterprise Time Management</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

  <style>
    :root {
      /* Enhanced Design Tokens */
      --primary-50: #eff6ff;
      --primary-100: #dbeafe;
      --primary-200: #bfdbfe;
      --primary-300: #93c5fd;
      --primary-400: #60a5fa;
      --primary-500: #3b82f6;
      --primary-600: #2563eb;
      --primary-700: #1d4ed8;
      --primary-800: #1e40af;
      --primary-900: #1e3a8a;

      --secondary-50: #f0f9ff;
      --secondary-100: #e0f2fe;
      --secondary-500: #06b6d4;
      --secondary-600: #0891b2;
      --secondary-700: #0e7490;

      --success-50: #f0fdf4;
      --success-100: #dcfce7;
      --success-500: #22c55e;
      --success-600: #16a34a;
      --success-700: #15803d;

      --warning-50: #fffbeb;
      --warning-100: #fef3c7;
      --warning-500: #f59e0b;
      --warning-600: #d97706;
      --warning-700: #b45309;

      --danger-50: #fef2f2;
      --danger-100: #fee2e2;
      --danger-500: #ef4444;
      --danger-600: #dc2626;
      --danger-700: #b91c1c;

      --purple-50: #faf5ff;
      --purple-100: #f3e8ff;
      --purple-500: #a855f7;
      --purple-600: #9333ea;

      --indigo-50: #eef2ff;
      --indigo-100: #e0e7ff;
      --indigo-500: #6366f1;
      --indigo-600: #4f46e5;

      --gray-50: #f9fafb;
      --gray-100: #f3f4f6;
      --gray-200: #e5e7eb;
      --gray-300: #d1d5db;
      --gray-400: #9ca3af;
      --gray-500: #6b7280;
      --gray-600: #4b5563;
      --gray-700: #374151;
      --gray-800: #1f2937;
      --gray-900: #111827;

      /* Semantic Colors */
      --bg-primary: var(--gray-50);
      --bg-secondary: #ffffff;
      --bg-tertiary: var(--gray-100);
      --text-primary: var(--gray-900);
      --text-secondary: var(--gray-600);
      --text-tertiary: var(--gray-400);
      --border-primary: var(--gray-200);
      --border-secondary: var(--gray-300);

      /* Enhanced Gradients */
      --gradient-primary: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
      --gradient-success: linear-gradient(135deg, var(--success-500), var(--success-600));
      --gradient-warning: linear-gradient(135deg, var(--warning-500), var(--warning-600));
      --gradient-danger: linear-gradient(135deg, var(--danger-500), var(--danger-600));
      --gradient-purple: linear-gradient(135deg, var(--purple-500), var(--indigo-500));
      --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));

      /* Glass Morphism */
      --glass-bg: rgba(255, 255, 255, 0.25);
      --glass-border: rgba(255, 255, 255, 0.18);
      --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

      /* Enhanced Shadows */
      --shadow-colored: 0 10px 25px -5px rgba(59, 130, 246, 0.25);
      --shadow-success: 0 10px 25px -5px rgba(34, 197, 94, 0.25);
      --shadow-warning: 0 10px 25px -5px rgba(245, 158, 11, 0.25);
      --shadow-danger: 0 10px 25px -5px rgba(239, 68, 68, 0.25);

      /* Spacing Scale */
      --space-1: 0.25rem;
      --space-2: 0.5rem;
      --space-3: 0.75rem;
      --space-4: 1rem;
      --space-5: 1.25rem;
      --space-6: 1.5rem;
      --space-8: 2rem;
      --space-10: 2.5rem;
      --space-12: 3rem;
      --space-16: 4rem;

      /* Typography Scale */
      --text-xs: 0.75rem;
      --text-sm: 0.875rem;
      --text-base: 1rem;
      --text-lg: 1.125rem;
      --text-xl: 1.25rem;
      --text-2xl: 1.5rem;
      --text-3xl: 1.875rem;
      --text-4xl: 2.25rem;

      /* Shadows */
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

      /* Border Radius */
      --radius-sm: 0.25rem;
      --radius-md: 0.375rem;
      --radius-lg: 0.5rem;
      --radius-xl: 0.75rem;
      --radius-2xl: 1rem;

      /* Transitions */
      --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
      --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
      --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Dark Theme */
    [data-theme="dark"] {
      --bg-primary: var(--gray-900);
      --bg-secondary: var(--gray-800);
      --bg-tertiary: var(--gray-700);
      --text-primary: var(--gray-50);
      --text-secondary: var(--gray-300);
      --text-tertiary: var(--gray-500);
      --border-primary: var(--gray-700);
      --border-secondary: var(--gray-600);
    }

    /* Reset & Base Styles */
    *, *::before, *::after {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    html {
      font-size: 16px;
      scroll-behavior: smooth;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: var(--text-primary);
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--gray-100) 100%);
      transition: background var(--transition-normal), color var(--transition-normal);
      overflow-x: hidden;
      font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
      font-variant-numeric: tabular-nums;
    }

    /* Accessibility */
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }

    /* Focus Management */
    :focus-visible {
      outline: 2px solid var(--primary-500);
      outline-offset: 2px;
      border-radius: var(--radius-sm);
    }

    /* Layout Components */
    .app-container {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .app-header {
      background: var(--glass-bg);
      -webkit-backdrop-filter: blur(20px);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid var(--glass-border);
      padding: var(--space-4) var(--space-6);
      box-shadow: var(--glass-shadow);
      position: sticky;
      top: 0;
      z-index: 50;
    }

    .header-content {
      max-width: 1440px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .app-logo {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      font-size: var(--text-xl);
      font-weight: 800;
      background: var(--gradient-primary);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-decoration: none;
      transition: all var(--transition-fast);
    }

    .app-logo:hover {
      transform: scale(1.05);
    }

    .app-logo i {
      font-size: var(--text-2xl);
      background: var(--gradient-primary);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: var(--space-4);
    }

    .live-clock {
      font-family: 'Inter', 'Courier New', monospace;
      font-size: var(--text-lg);
      font-weight: 600;
      color: var(--text-primary);
      padding: var(--space-3) var(--space-5);
      background: var(--glass-bg);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      border-radius: var(--radius-xl);
      border: 1px solid var(--glass-border);
      box-shadow: var(--shadow-md);
      font-variant-numeric: tabular-nums;
      letter-spacing: 0.05em;
      transition: all var(--transition-fast);
    }

    .live-clock:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-lg);
    }

    .theme-toggle {
      background: var(--glass-bg);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-xl);
      padding: var(--space-3) var(--space-4);
      color: var(--text-primary);
      cursor: pointer;
      transition: all var(--transition-fast);
      display: flex;
      align-items: center;
      gap: var(--space-2);
      font-weight: 500;
      box-shadow: var(--shadow-sm);
    }

    .theme-toggle:hover {
      background: var(--primary-500);
      color: white;
      transform: translateY(-1px);
      box-shadow: var(--shadow-colored);
    }

    .app-main {
      flex: 1;
      display: flex;
      max-width: 1440px;
      margin: 0 auto;
      width: 100%;
      padding: var(--space-6);
      gap: var(--space-6);
    }

    /* Navigation Tabs */
    .nav-tabs {
      display: flex;
      background: var(--bg-secondary);
      border-radius: var(--radius-xl);
      padding: var(--space-1);
      margin-bottom: var(--space-6);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border-primary);
      overflow-x: auto;
    }

    .nav-tab {
      background: none;
      border: none;
      padding: var(--space-3) var(--space-5);
      border-radius: var(--radius-lg);
      color: var(--text-secondary);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
      white-space: nowrap;
      display: flex;
      align-items: center;
      gap: var(--space-2);
      min-width: 44px;
      min-height: 44px;
    }

    .nav-tab:hover {
      background: var(--bg-tertiary);
      color: var(--text-primary);
    }

    .nav-tab.active {
      background: var(--primary-500);
      color: white;
      box-shadow: var(--shadow-md);
    }

    .tab-content {
      display: none;
      animation: fadeIn var(--transition-normal);
    }

    .tab-content.active {
      display: block;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* Enhanced Card Components */
    .card {
      background: var(--glass-bg);
      -webkit-backdrop-filter: blur(20px);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-2xl);
      box-shadow: var(--glass-shadow);
      overflow: hidden;
      transition: all var(--transition-fast);
      position: relative;
    }

    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    }

    .card:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-xl);
    }

    .card-premium {
      background: var(--gradient-purple);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .card-premium::before {
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    }

    .card-premium .card-title,
    .card-premium .stat-label {
      color: rgba(255, 255, 255, 0.9);
    }

    .card-header {
      padding: var(--space-6);
      border-bottom: 1px solid var(--border-primary);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .card-title {
      font-size: var(--text-xl);
      font-weight: 600;
      color: var(--text-primary);
      display: flex;
      align-items: center;
      gap: var(--space-3);
    }

    .card-body {
      padding: var(--space-6);
    }

    /* Enhanced Button System */
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: var(--space-2);
      padding: var(--space-3) var(--space-5);
      border: 1px solid transparent;
      border-radius: var(--radius-xl);
      font-size: var(--text-sm);
      font-weight: 600;
      text-decoration: none;
      cursor: pointer;
      transition: all var(--transition-fast);
      min-width: 44px;
      min-height: 44px;
      position: relative;
      overflow: hidden;
      font-family: 'Inter', sans-serif;
      letter-spacing: 0.025em;
    }

    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .btn:hover::before {
      left: 100%;
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .btn:disabled::before {
      display: none;
    }

    .btn-primary {
      background: var(--gradient-primary);
      color: white;
      box-shadow: var(--shadow-colored);
    }

    .btn-primary:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: var(--shadow-xl);
    }

    .btn-secondary {
      background: var(--bg-tertiary);
      color: var(--text-primary);
      border-color: var(--border-primary);
    }

    .btn-secondary:hover:not(:disabled) {
      background: var(--bg-secondary);
      border-color: var(--border-secondary);
    }

    .btn-success {
      background: var(--success-500);
      color: white;
    }

    .btn-success:hover:not(:disabled) {
      background: var(--success-600);
    }

    .btn-warning {
      background: var(--warning-500);
      color: white;
    }

    .btn-warning:hover:not(:disabled) {
      background: var(--warning-600);
    }

    .btn-danger {
      background: var(--danger-500);
      color: white;
    }

    .btn-danger:hover:not(:disabled) {
      background: var(--danger-600);
    }

    .btn-sm {
      padding: var(--space-2) var(--space-3);
      font-size: var(--text-xs);
      min-width: 36px;
      min-height: 36px;
    }

    .btn-lg {
      padding: var(--space-4) var(--space-6);
      font-size: var(--text-lg);
      min-width: 52px;
      min-height: 52px;
    }

    /* Form Components */
    .form-group {
      margin-bottom: var(--space-5);
    }

    .form-label {
      display: block;
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--text-primary);
      margin-bottom: var(--space-2);
    }

    .form-input, .form-select, .form-textarea {
      width: 100%;
      padding: var(--space-3) var(--space-4);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      background: var(--bg-secondary);
      color: var(--text-primary);
      font-size: var(--text-base);
      transition: all var(--transition-fast);
    }

    .form-input:focus, .form-select:focus, .form-textarea:focus {
      outline: none;
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-textarea {
      resize: vertical;
      min-height: 100px;
    }

    /* Enhanced Timer Display */
    .timer-display {
      text-align: center;
      padding: var(--space-10);
      background: var(--gradient-primary);
      border-radius: var(--radius-2xl);
      border: 1px solid rgba(255, 255, 255, 0.2);
      margin-bottom: var(--space-6);
      position: relative;
      overflow: hidden;
      box-shadow: var(--shadow-colored);
    }

    .timer-display::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%);
      pointer-events: none;
    }

    .timer-display::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      animation: rotate 10s linear infinite;
      pointer-events: none;
    }

    @keyframes rotate {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    [data-theme="dark"] .timer-display {
      background: var(--gradient-purple);
      border-color: rgba(255, 255, 255, 0.1);
    }

    .timer-time {
      font-family: 'Inter', 'Courier New', monospace;
      font-size: 4rem;
      font-weight: 800;
      color: white;
      margin-bottom: var(--space-4);
      letter-spacing: 0.1em;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      position: relative;
      z-index: 1;
      font-variant-numeric: tabular-nums;
    }

    @media (max-width: 768px) {
      .timer-time {
        font-size: 2.5rem;
      }
    }

    .timer-controls {
      display: flex;
      justify-content: center;
      gap: var(--space-4);
      flex-wrap: wrap;
    }

    .timer-status {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      border-radius: var(--radius-lg);
      font-size: var(--text-sm);
      font-weight: 500;
      margin-bottom: var(--space-4);
    }

    .timer-status.running {
      background: var(--success-50);
      color: var(--success-600);
      border: 1px solid var(--success-200);
    }

    .timer-status.paused {
      background: var(--warning-50);
      color: var(--warning-600);
      border: 1px solid var(--warning-200);
    }

    .timer-status.stopped {
      background: var(--gray-50);
      color: var(--gray-600);
      border: 1px solid var(--gray-200);
    }

    /* Task Management */
    .task-list {
      display: flex;
      flex-direction: column;
      gap: var(--space-4);
    }

    .task-item {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-xl);
      padding: var(--space-5);
      transition: all var(--transition-fast);
      position: relative;
      cursor: pointer;
    }

    .task-item:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }

    .task-item.completed {
      opacity: 0.7;
      background: var(--success-50);
      border-color: var(--success-200);
    }

    .task-item.in-progress {
      border-left: 4px solid var(--primary-500);
    }

    .task-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: var(--space-3);
    }

    .task-title {
      font-size: var(--text-lg);
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--space-1);
      flex: 1;
    }

    .task-priority {
      padding: var(--space-1) var(--space-3);
      border-radius: var(--radius-md);
      font-size: var(--text-xs);
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .task-priority.high {
      background: var(--danger-50);
      color: var(--danger-600);
      border: 1px solid var(--danger-200);
    }

    .task-priority.medium {
      background: var(--warning-50);
      color: var(--warning-600);
      border: 1px solid var(--warning-200);
    }

    .task-priority.low {
      background: var(--success-50);
      color: var(--success-600);
      border: 1px solid var(--success-200);
    }

    .task-meta {
      display: flex;
      align-items: center;
      gap: var(--space-4);
      font-size: var(--text-sm);
      color: var(--text-secondary);
      margin-bottom: var(--space-3);
    }

    .task-meta-item {
      display: flex;
      align-items: center;
      gap: var(--space-1);
    }

    .task-description {
      color: var(--text-secondary);
      margin-bottom: var(--space-3);
      line-height: 1.5;
    }

    .task-tags {
      display: flex;
      flex-wrap: wrap;
      gap: var(--space-2);
      margin-bottom: var(--space-3);
    }

    .task-tag {
      padding: var(--space-1) var(--space-2);
      background: var(--primary-50);
      color: var(--primary-600);
      border: 1px solid var(--primary-200);
      border-radius: var(--radius-md);
      font-size: var(--text-xs);
    }

    .task-actions {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      margin-top: var(--space-3);
    }

    /* Grid Layouts */
    .grid {
      display: grid;
      gap: var(--space-6);
    }

    .grid-cols-1 { grid-template-columns: 1fr; }
    .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

    /* Enhanced Stats Cards */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--space-6);
      margin-bottom: var(--space-8);
    }

    .stat-card {
      background: var(--glass-bg);
      -webkit-backdrop-filter: blur(20px);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-2xl);
      padding: var(--space-8);
      text-align: center;
      transition: all var(--transition-fast);
      position: relative;
      overflow: hidden;
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: var(--gradient-primary);
    }

    .stat-card:hover {
      transform: translateY(-4px) scale(1.02);
      box-shadow: var(--shadow-xl);
    }

    .stat-card.premium {
      background: var(--gradient-purple);
      color: white;
    }

    .stat-card.premium::before {
      background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.3));
    }

    .stat-subtitle {
      font-size: var(--text-xs);
      opacity: 0.8;
      margin-top: var(--space-2);
      font-weight: 500;
    }

    /* AI Insights */
    .insights-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--space-4);
    }

    .insight-card {
      background: var(--glass-bg);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-xl);
      padding: var(--space-5);
      display: flex;
      align-items: flex-start;
      gap: var(--space-4);
      transition: all var(--transition-fast);
    }

    .insight-card:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }

    .insight-icon {
      font-size: var(--text-2xl);
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--gradient-primary);
      border-radius: var(--radius-xl);
      flex-shrink: 0;
    }

    .insight-content h4 {
      margin: 0 0 var(--space-2) 0;
      font-size: var(--text-lg);
      font-weight: 600;
      color: var(--text-primary);
    }

    .insight-content p {
      margin: 0;
      color: var(--text-secondary);
      line-height: 1.5;
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: var(--radius-xl);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--space-4);
      font-size: var(--text-xl);
    }

    .stat-icon.primary {
      background: var(--primary-50);
      color: var(--primary-600);
    }

    .stat-icon.success {
      background: var(--success-50);
      color: var(--success-600);
    }

    .stat-icon.warning {
      background: var(--warning-50);
      color: var(--warning-600);
    }

    .stat-value {
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: var(--space-2);
    }

    .stat-label {
      font-size: var(--text-sm);
      color: var(--text-secondary);
      font-weight: 500;
    }

    /* Progress Bars */
    .progress {
      width: 100%;
      height: 8px;
      background: var(--gray-200);
      border-radius: var(--radius-md);
      overflow: hidden;
      margin: var(--space-3) 0;
    }

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
      border-radius: var(--radius-md);
      transition: width var(--transition-normal);
    }

    /* Floating Action Button */
    .fab {
      position: fixed;
      bottom: var(--space-6);
      right: var(--space-6);
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background: var(--primary-500);
      color: white;
      border: none;
      box-shadow: var(--shadow-xl);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--text-xl);
      transition: all var(--transition-fast);
      z-index: 40;
    }

    .fab:hover {
      background: var(--primary-600);
      transform: scale(1.1);
    }

    /* Modal System */
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 100;
      opacity: 0;
      visibility: hidden;
      transition: all var(--transition-normal);
    }

    .modal.active {
      opacity: 1;
      visibility: visible;
    }

    .modal-content {
      background: var(--bg-secondary);
      border-radius: var(--radius-2xl);
      box-shadow: var(--shadow-xl);
      max-width: 600px;
      width: 90%;
      max-height: 90vh;
      overflow-y: auto;
      transform: scale(0.9);
      transition: transform var(--transition-normal);
    }

    .modal.active .modal-content {
      transform: scale(1);
    }

    .modal-header {
      padding: var(--space-6);
      border-bottom: 1px solid var(--border-primary);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .modal-title {
      font-size: var(--text-xl);
      font-weight: 600;
      color: var(--text-primary);
    }

    .modal-close {
      background: none;
      border: none;
      font-size: var(--text-xl);
      color: var(--text-secondary);
      cursor: pointer;
      padding: var(--space-2);
      border-radius: var(--radius-md);
      transition: all var(--transition-fast);
    }

    .modal-close:hover {
      background: var(--bg-tertiary);
      color: var(--text-primary);
    }

    .modal-body {
      padding: var(--space-6);
    }

    .modal-footer {
      padding: var(--space-6);
      border-top: 1px solid var(--border-primary);
      display: flex;
      justify-content: flex-end;
      gap: var(--space-3);
    }

    /* Toast Notifications */
    .toast-container {
      position: fixed;
      top: var(--space-6);
      right: var(--space-6);
      z-index: 200;
      display: flex;
      flex-direction: column;
      gap: var(--space-3);
    }

    .toast {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-xl);
      padding: var(--space-4) var(--space-5);
      box-shadow: var(--shadow-lg);
      display: flex;
      align-items: center;
      gap: var(--space-3);
      min-width: 300px;
      transform: translateX(100%);
      transition: transform var(--transition-normal);
    }

    .toast.show {
      transform: translateX(0);
    }

    .toast.success {
      border-left: 4px solid var(--success-500);
    }

    .toast.error {
      border-left: 4px solid var(--danger-500);
    }

    .toast.warning {
      border-left: 4px solid var(--warning-500);
    }

    .toast.info {
      border-left: 4px solid var(--primary-500);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
      .app-main {
        flex-direction: column;
        padding: var(--space-4);
      }

      .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      }

      .grid-cols-4 {
        grid-template-columns: repeat(2, 1fr);
      }

      .grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .app-header {
        padding: var(--space-3) var(--space-4);
      }

      .header-content {
        flex-direction: column;
        gap: var(--space-3);
        align-items: stretch;
      }

      .header-actions {
        justify-content: space-between;
      }

      .app-main {
        padding: var(--space-3);
        gap: var(--space-4);
      }

      .nav-tabs {
        margin-bottom: var(--space-4);
      }

      .timer-time {
        font-size: var(--text-3xl);
      }

      .timer-controls {
        flex-direction: column;
        align-items: stretch;
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
      }

      .grid-cols-2,
      .grid-cols-3,
      .grid-cols-4 {
        grid-template-columns: 1fr;
      }

      .task-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
      }

      .task-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
      }

      .fab {
        bottom: var(--space-4);
        right: var(--space-4);
      }

      .modal-content {
        width: 95%;
        margin: var(--space-4);
      }

      .toast-container {
        top: var(--space-4);
        right: var(--space-4);
        left: var(--space-4);
      }

      .toast {
        min-width: auto;
      }
    }

    @media (max-width: 480px) {
      .app-logo {
        font-size: var(--text-lg);
      }

      .live-clock {
        font-size: var(--text-base);
      }

      .timer-display {
        padding: var(--space-6);
      }

      .timer-time {
        font-size: var(--text-2xl);
      }

      .card-header,
      .card-body {
        padding: var(--space-4);
      }

      .modal-header,
      .modal-body,
      .modal-footer {
        padding: var(--space-4);
      }
    }

    /* Utility Classes */
    .text-center { text-align: center; }
    .text-left { text-align: left; }
    .text-right { text-align: right; }

    .w-auto { width: auto; }

    .flex { display: flex; }
    .flex-col { flex-direction: column; }
    .items-center { align-items: center; }
    .justify-center { justify-content: center; }
    .justify-between { justify-content: space-between; }

    .hidden { display: none; }
    .block { display: block; }
    .inline-block { display: inline-block; }

    .w-full { width: 100%; }
    .h-full { height: 100%; }

    .mb-0 { margin-bottom: 0; }
    .mb-2 { margin-bottom: var(--space-2); }
    .mb-4 { margin-bottom: var(--space-4); }
    .mb-6 { margin-bottom: var(--space-6); }

    .mt-0 { margin-top: 0; }
    .mt-2 { margin-top: var(--space-2); }
    .mt-4 { margin-top: var(--space-4); }
    .mt-6 { margin-top: var(--space-6); }

    .p-0 { padding: 0; }
    .p-2 { padding: var(--space-2); }
    .p-4 { padding: var(--space-4); }
    .p-6 { padding: var(--space-6); }


  </style>
</head>
<body>
  <div class="app-container">
    <!-- Header -->
    <header class="app-header">
      <div class="header-content">
        <a href="#" class="app-logo">
          <i class="fas fa-clock"></i>
          TimeTracker Pro
        </a>
        <div class="header-actions">
          <div class="live-clock" id="liveClock">00:00:00</div>
          <button type="button" class="theme-toggle" id="themeToggle" aria-label="Toggle theme">
            <i class="fas fa-moon"></i>
            <span>Dark</span>
          </button>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="app-main">
      <div class="w-full">
        <!-- Navigation Tabs -->
        <nav class="nav-tabs" role="tablist">
          <button type="button" class="nav-tab active" data-tab="dashboard" role="tab" aria-selected="true">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
          </button>
          <button type="button" class="nav-tab" data-tab="tasks" role="tab" aria-selected="false">
            <i class="fas fa-tasks"></i>
            Tasks
          </button>
          <button type="button" class="nav-tab" data-tab="analytics" role="tab" aria-selected="false">
            <i class="fas fa-chart-line"></i>
            Analytics
          </button>
          <button type="button" class="nav-tab" data-tab="reports" role="tab" aria-selected="false">
            <i class="fas fa-file-alt"></i>
            Reports
          </button>
          <button type="button" class="nav-tab" data-tab="settings" role="tab" aria-selected="false">
            <i class="fas fa-cog"></i>
            Settings
          </button>
        </nav>

        <!-- Dashboard Tab -->
        <div id="dashboard-tab" class="tab-content active" role="tabpanel">
          <!-- Enhanced Stats Overview -->
          <div class="stats-grid">
            <div class="stat-card premium">
              <div class="stat-icon primary">
                <i class="fas fa-clock"></i>
              </div>
              <div class="stat-value" id="todayTime">0h 0m</div>
              <div class="stat-label">Today's Focus Time</div>
              <div class="progress">
                <div class="progress-bar" id="todayProgress"></div>
              </div>
              <div class="stat-subtitle" id="goalProgress">0% of daily goal</div>
            </div>

            <div class="stat-card">
              <div class="stat-icon success">
                <i class="fas fa-trophy"></i>
              </div>
              <div class="stat-value" id="completedTasks">0</div>
              <div class="stat-label">Tasks Completed</div>
              <div class="stat-subtitle" id="completionStreak">🔥 0 day streak</div>
            </div>

            <div class="stat-card">
              <div class="stat-icon warning">
                <i class="fas fa-rocket"></i>
              </div>
              <div class="stat-value" id="activeTasks">0</div>
              <div class="stat-label">Active Projects</div>
              <div class="stat-subtitle" id="urgentTasks">0 urgent</div>
            </div>

            <div class="stat-card">
              <div class="stat-icon primary">
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="stat-value" id="efficiency">0%</div>
              <div class="stat-label">Productivity Score</div>
              <div class="stat-subtitle" id="efficiencyTrend">📈 +0% vs yesterday</div>
            </div>
          </div>

          <!-- AI Insights Panel -->
          <div class="card mb-6">
            <div class="card-header">
              <h2 class="card-title">
                <i class="fas fa-brain"></i>
                AI Productivity Insights
              </h2>
              <button type="button" class="btn btn-sm btn-secondary" id="refreshInsightsBtn">
                <i class="fas fa-sync-alt"></i>
                Refresh
              </button>
            </div>
            <div class="card-body">
              <div class="insights-grid" id="aiInsights">
                <div class="insight-card">
                  <div class="insight-icon">🎯</div>
                  <div class="insight-content">
                    <h4>Focus Recommendation</h4>
                    <p id="focusInsight">Analyzing your work patterns...</p>
                  </div>
                </div>
                <div class="insight-card">
                  <div class="insight-icon">⚡</div>
                  <div class="insight-content">
                    <h4>Peak Performance</h4>
                    <p id="peakInsight">Identifying your most productive hours...</p>
                  </div>
                </div>
                <div class="insight-card">
                  <div class="insight-icon">🎨</div>
                  <div class="insight-content">
                    <h4>Work-Life Balance</h4>
                    <p id="balanceInsight">Evaluating your schedule balance...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Timer Section -->
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">
                <i class="fas fa-stopwatch"></i>
                Active Timer
              </h2>
              <div class="timer-status stopped" id="timerStatus">
                <i class="fas fa-stop-circle"></i>
                Stopped
              </div>
            </div>
            <div class="card-body">
              <div class="timer-display">
                <div class="timer-time" id="timerDisplay">00:00:00</div>
                <div class="timer-controls">
                  <button type="button" class="btn btn-success btn-lg" id="startBtn">
                    <i class="fas fa-play"></i>
                    Start
                  </button>
                  <button type="button" class="btn btn-warning btn-lg" id="pauseBtn" disabled>
                    <i class="fas fa-pause"></i>
                    Pause
                  </button>
                  <button type="button" class="btn btn-danger btn-lg" id="stopBtn" disabled>
                    <i class="fas fa-stop"></i>
                    Stop
                  </button>
                </div>
              </div>

              <!-- Active Task Selection -->
              <div class="form-group">
                <label class="form-label" for="activeTaskSelect">Active Task</label>
                <select class="form-select" id="activeTaskSelect">
                  <option value="">Select a task...</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="card mt-6">
            <div class="card-header">
              <h2 class="card-title">
                <i class="fas fa-history"></i>
                Recent Activity
              </h2>
            </div>
            <div class="card-body">
              <div id="recentActivity" class="space-y-4">
                <!-- Activity items will be populated here -->
              </div>
            </div>
          </div>
        </div>

        <!-- Tasks Tab -->
        <div id="tasks-tab" class="tab-content" role="tabpanel">
          <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">Smart Task Management</h1>
            <div class="flex gap-3">
              <button type="button" class="btn btn-secondary" id="bulkActionsBtn">
                <i class="fas fa-tasks"></i>
                Bulk Actions
              </button>
              <button type="button" class="btn btn-primary" id="addTaskBtn">
                <i class="fas fa-plus"></i>
                Add Task
              </button>
            </div>
          </div>

          <!-- Task Filters -->
          <div class="card mb-6">
            <div class="card-body">
              <div class="grid grid-cols-4">
                <div class="form-group">
                  <label class="form-label" for="filterStatus">Status</label>
                  <select class="form-select" id="filterStatus">
                    <option value="">All Status</option>
                    <option value="not-started">Not Started</option>
                    <option value="in-progress">In Progress</option>
                    <option value="paused">Paused</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label" for="filterPriority">Priority</label>
                  <select class="form-select" id="filterPriority">
                    <option value="">All Priorities</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label" for="filterCategory">Category</label>
                  <select class="form-select" id="filterCategory">
                    <option value="">All Categories</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label" for="searchTasks">Search</label>
                  <input type="text" class="form-input" id="searchTasks" placeholder="Search tasks...">
                </div>
              </div>
            </div>
          </div>

          <!-- Task List -->
          <div class="task-list" id="taskList">
            <!-- Tasks will be populated here -->
          </div>
        </div>

        <!-- Analytics Tab -->
        <div id="analytics-tab" class="tab-content" role="tabpanel">
          <h1 class="text-3xl font-bold mb-6">Analytics & Insights</h1>

          <!-- Time Period Selector -->
          <div class="card mb-6">
            <div class="card-body">
              <div class="flex items-center gap-4">
                <label class="form-label mb-0" for="analyticsPeriod">Time Period:</label>
                <select class="form-select w-auto" id="analyticsPeriod">
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="quarter">This Quarter</option>
                  <option value="year">This Year</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Charts Grid -->
          <div class="grid grid-cols-2 mb-6">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Time Distribution</h3>
              </div>
              <div class="card-body">
                <canvas id="timeDistributionChart" width="400" height="300"></canvas>
              </div>
            </div>

            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Productivity Trend</h3>
              </div>
              <div class="card-body">
                <canvas id="productivityChart" width="400" height="300"></canvas>
              </div>
            </div>
          </div>

          <!-- Detailed Analytics -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">Detailed Breakdown</h3>
            </div>
            <div class="card-body">
              <div id="analyticsDetails">
                <!-- Analytics details will be populated here -->
              </div>
            </div>
          </div>
        </div>

        <!-- Reports Tab -->
        <div id="reports-tab" class="tab-content" role="tabpanel">
          <h1 class="text-3xl font-bold mb-6">Reports & Export</h1>

          <div class="grid grid-cols-2">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Generate Report</h3>
              </div>
              <div class="card-body">
                <div class="form-group">
                  <label class="form-label" for="reportType">Report Type</label>
                  <select class="form-select" id="reportType">
                    <option value="daily">Daily Summary</option>
                    <option value="weekly">Weekly Report</option>
                    <option value="monthly">Monthly Report</option>
                    <option value="project">Project Report</option>
                    <option value="detailed">Detailed Time Log</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label" for="reportFormat">Format</label>
                  <select class="form-select" id="reportFormat">
                    <option value="pdf">PDF</option>
                    <option value="csv">CSV</option>
                    <option value="json">JSON</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label" for="reportDateRange">Date Range</label>
                  <div class="flex gap-2">
                    <input type="date" class="form-input" id="reportStartDate">
                    <input type="date" class="form-input" id="reportEndDate">
                  </div>
                </div>

                <button type="button" class="btn btn-primary w-full" id="generateReportBtn">
                  <i class="fas fa-file-download"></i>
                  Generate Report
                </button>
              </div>
            </div>

            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Quick Actions</h3>
              </div>
              <div class="card-body">
                <div class="space-y-3">
                  <button type="button" class="btn btn-secondary w-full" id="exportAllDataBtn">
                    <i class="fas fa-database"></i>
                    Export All Data
                  </button>

                  <button type="button" class="btn btn-secondary w-full" id="importDataBtn">
                    <i class="fas fa-upload"></i>
                    Import Data
                  </button>

                  <button type="button" class="btn btn-warning w-full" id="backupDataBtn">
                    <i class="fas fa-save"></i>
                    Create Backup
                  </button>

                  <button type="button" class="btn btn-danger w-full" id="clearDataBtn">
                    <i class="fas fa-trash"></i>
                    Clear All Data
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings-tab" class="tab-content" role="tabpanel">
          <h1 class="text-3xl font-bold mb-6">Settings</h1>

          <div class="grid grid-cols-2">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">General Settings</h3>
              </div>
              <div class="card-body">
                <div class="form-group">
                  <label class="form-label" for="workingHours">Daily Working Hours Goal</label>
                  <input type="number" class="form-input" id="workingHours" min="1" max="24" value="8">
                </div>

                <div class="form-group">
                  <label class="form-label" for="timezone">Timezone</label>
                  <select class="form-select" id="timezone">
                    <option value="auto">Auto-detect</option>
                    <option value="UTC">UTC</option>
                    <option value="America/New_York">Eastern Time</option>
                    <option value="America/Chicago">Central Time</option>
                    <option value="America/Denver">Mountain Time</option>
                    <option value="America/Los_Angeles">Pacific Time</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label">
                    <input type="checkbox" id="autoSave" checked>
                    Auto-save data
                  </label>
                </div>

                <div class="form-group">
                  <label class="form-label">
                    <input type="checkbox" id="idleDetection" checked>
                    Idle time detection
                  </label>
                </div>

                <div class="form-group">
                  <label class="form-label" for="idleThreshold">Idle threshold (minutes)</label>
                  <input type="number" class="form-input" id="idleThreshold" min="1" max="60" value="5">
                </div>
              </div>
            </div>

            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Notifications</h3>
              </div>
              <div class="card-body">
                <div class="form-group">
                  <label class="form-label">
                    <input type="checkbox" id="pomodoroNotifications" checked>
                    Pomodoro break reminders
                  </label>
                </div>

                <div class="form-group">
                  <label class="form-label">
                    <input type="checkbox" id="dailyGoalNotifications" checked>
                    Daily goal notifications
                  </label>
                </div>

                <div class="form-group">
                  <label class="form-label">
                    <input type="checkbox" id="taskDeadlineNotifications" checked>
                    Task deadline reminders
                  </label>
                </div>

                <div class="form-group">
                  <label class="form-label" for="notificationSound">Notification Sound</label>
                  <select class="form-select" id="notificationSound">
                    <option value="none">None</option>
                    <option value="chime">Chime</option>
                    <option value="bell">Bell</option>
                    <option value="notification">Notification</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Floating Action Button -->
    <button type="button" class="fab" id="quickTimerBtn" aria-label="Quick timer">
      <i class="fas fa-play"></i>
    </button>

    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>
  </div>

  <!-- Task Modal -->
  <div class="modal" id="taskModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title" id="taskModalTitle">Add New Task</h3>
        <button type="button" class="modal-close" aria-label="Close modal">&times;</button>
      </div>
      <div class="modal-body">
        <form id="taskForm">
          <div class="form-group">
            <label class="form-label" for="taskTitle">Title *</label>
            <input type="text" class="form-input" id="taskTitle" required maxlength="100" placeholder="Enter task title...">
          </div>

          <div class="form-group">
            <label class="form-label" for="taskDescription">Description</label>
            <textarea class="form-textarea" id="taskDescription" placeholder="Enter task description..."></textarea>
          </div>

          <div class="grid grid-cols-2">
            <div class="form-group">
              <label class="form-label" for="taskPriority">Priority</label>
              <select class="form-select" id="taskPriority">
                <option value="low">Low</option>
                <option value="medium" selected>Medium</option>
                <option value="high">High</option>
              </select>
            </div>

            <div class="form-group">
              <label class="form-label" for="taskCategory">Category</label>
              <select class="form-select" id="taskCategory">
                <option value="work">Work</option>
                <option value="personal">Personal</option>
                <option value="learning">Learning</option>
                <option value="health">Health</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>

          <div class="grid grid-cols-2">
            <div class="form-group">
              <label class="form-label" for="taskEstimate">Estimated Duration</label>
              <div class="flex gap-2">
                <input type="number" class="form-input" id="taskEstimateHours" min="0" max="23" placeholder="Hours">
                <input type="number" class="form-input" id="taskEstimateMinutes" min="0" max="59" placeholder="Minutes">
              </div>
            </div>

            <div class="form-group">
              <label class="form-label" for="taskDueDate">Due Date</label>
              <input type="datetime-local" class="form-input" id="taskDueDate">
            </div>
          </div>

          <div class="form-group">
            <label class="form-label" for="taskTags">Tags (comma-separated)</label>
            <input type="text" class="form-input" id="taskTags" placeholder="urgent, client-work, review">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" id="cancelTaskBtn">Cancel</button>
        <button type="button" class="btn btn-primary" id="saveTaskBtn">Save Task</button>
      </div>
    </div>
  </div>

  <!-- Confirmation Modal -->
  <div class="modal" id="confirmModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title" id="confirmModalTitle">Confirm Action</h3>
        <button type="button" class="modal-close" aria-label="Close modal">&times;</button>
      </div>
      <div class="modal-body">
        <p id="confirmModalMessage">Are you sure you want to perform this action?</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" id="confirmCancelBtn">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmActionBtn">Confirm</button>
      </div>
    </div>
  </div>

  <!-- Time Entry Modal -->
  <div class="modal" id="timeEntryModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">Manual Time Entry</h3>
        <button type="button" class="modal-close" aria-label="Close modal">&times;</button>
      </div>
      <div class="modal-body">
        <form id="timeEntryForm">
          <div class="form-group">
            <label class="form-label" for="timeEntryTask">Task</label>
            <select class="form-select" id="timeEntryTask" required>
              <option value="">Select a task...</option>
            </select>
          </div>

          <div class="grid grid-cols-2">
            <div class="form-group">
              <label class="form-label" for="timeEntryDate">Date</label>
              <input type="date" class="form-input" id="timeEntryDate" required>
            </div>

            <div class="form-group">
              <label class="form-label" for="timeEntryDuration">Duration</label>
              <div class="flex gap-2">
                <input type="number" class="form-input" id="timeEntryHours" min="0" max="23" placeholder="Hours" required>
                <input type="number" class="form-input" id="timeEntryMinutes" min="0" max="59" placeholder="Minutes" required>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label" for="timeEntryDescription">Description</label>
            <textarea class="form-textarea" id="timeEntryDescription" placeholder="What did you work on?"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" id="cancelTimeEntryBtn">Cancel</button>
        <button type="button" class="btn btn-primary" id="saveTimeEntryBtn">Save Entry</button>
      </div>
    </div>
  </div>

  <!-- Pomodoro Modal -->
  <div class="modal" id="pomodoroModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">Pomodoro Session</h3>
        <button type="button" class="modal-close" aria-label="Close modal">&times;</button>
      </div>
      <div class="modal-body text-center">
        <div class="timer-display">
          <div class="timer-time" id="pomodoroTimer">25:00</div>
          <div class="timer-status" id="pomodoroStatus">
            <i class="fas fa-tomato"></i>
            Focus Time
          </div>
        </div>

        <div class="form-group">
          <label class="form-label" for="pomodoroTask">Working on:</label>
          <select class="form-select" id="pomodoroTask">
            <option value="">Select a task...</option>
          </select>
        </div>

        <div class="timer-controls">
          <button type="button" class="btn btn-success" id="startPomodoroBtn">
            <i class="fas fa-play"></i>
            Start Focus
          </button>
          <button type="button" class="btn btn-warning" id="pausePomodoroBtn" disabled>
            <i class="fas fa-pause"></i>
            Pause
          </button>
          <button type="button" class="btn btn-danger" id="stopPomodoroBtn" disabled>
            <i class="fas fa-stop"></i>
            Stop
          </button>
        </div>
      </div>
    </div>
  </div>

  <script>
    // ===== APPLICATION STATE MANAGEMENT =====
    class TimeTrackerApp {
      constructor() {
        this.state = {
          tasks: [],
          timeEntries: [],
          activeTimer: null,
          currentTask: null,
          settings: {
            workingHours: 8,
            timezone: 'auto',
            autoSave: true,
            idleDetection: true,
            idleThreshold: 5,
            pomodoroNotifications: true,
            dailyGoalNotifications: true,
            taskDeadlineNotifications: true,
            notificationSound: 'chime',
            theme: 'light'
          },
          analytics: {
            todayTime: 0,
            completedTasks: 0,
            activeTasks: 0,
            efficiency: 0
          }
        };

        this.timers = {
          main: null,
          pomodoro: null,
          clock: null,
          autoSave: null
        };

        this.charts = {};
        this.currentTab = 'dashboard';
        this.isInitialized = false;
        this.aiInsights = {
          focus: '',
          peak: '',
          balance: ''
        };
        this.streaks = {
          completion: 0,
          focus: 0
        };

        this.init();
      }

      // ===== INITIALIZATION =====
      async init() {
        try {
          await this.loadData();
          this.setupEventListeners();
          this.initializeCharts();
          this.startClockTimer();
          this.startAutoSave();
          this.loadSampleData();
          this.updateUI();
          this.isInitialized = true;
          this.showToast('TimeTracker Pro initialized successfully!', 'success');
        } catch (error) {
          console.error('Initialization error:', error);
          this.showToast('Error initializing application', 'error');
        }
      }

      // ===== DATA MANAGEMENT =====
      async loadData() {
        try {
          const savedData = localStorage.getItem('timetracker-data');
          if (savedData) {
            const data = JSON.parse(savedData);
            this.state = { ...this.state, ...data };
          }

          // Apply theme
          this.applyTheme(this.state.settings.theme);
        } catch (error) {
          console.error('Error loading data:', error);
        }
      }

      saveData() {
        try {
          const dataToSave = {
            tasks: this.state.tasks,
            timeEntries: this.state.timeEntries,
            settings: this.state.settings
          };
          localStorage.setItem('timetracker-data', JSON.stringify(dataToSave));
          localStorage.setItem('timetracker-backup', JSON.stringify({
            ...dataToSave,
            timestamp: Date.now()
          }));
        } catch (error) {
          console.error('Error saving data:', error);
          this.showToast('Error saving data', 'error');
        }
      }

      loadSampleData() {
        if (this.state.tasks.length === 0) {
          const sampleTasks = [
            {
              id: this.generateId(),
              title: 'Complete project proposal',
              description: 'Write and review the Q4 project proposal for the new client initiative.',
              priority: 'high',
              category: 'work',
              status: 'in-progress',
              estimatedDuration: 120, // minutes
              actualDuration: 45,
              dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
              tags: ['urgent', 'client-work'],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            {
              id: this.generateId(),
              title: 'Review team performance',
              description: 'Conduct quarterly performance reviews for team members.',
              priority: 'medium',
              category: 'work',
              status: 'not-started',
              estimatedDuration: 180,
              actualDuration: 0,
              dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
              tags: ['management', 'quarterly'],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            {
              id: this.generateId(),
              title: 'Learn new framework',
              description: 'Study and practice with the latest React features and best practices.',
              priority: 'low',
              category: 'learning',
              status: 'in-progress',
              estimatedDuration: 300,
              actualDuration: 120,
              dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
              tags: ['react', 'development', 'skill-building'],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            {
              id: this.generateId(),
              title: 'Gym workout',
              description: 'Complete full-body strength training session.',
              priority: 'medium',
              category: 'health',
              status: 'completed',
              estimatedDuration: 60,
              actualDuration: 65,
              dueDate: new Date().toISOString(),
              tags: ['fitness', 'health'],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          ];

          this.state.tasks = sampleTasks;

          // Add some sample time entries
          const sampleTimeEntries = [
            {
              id: this.generateId(),
              taskId: sampleTasks[0].id,
              startTime: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
              endTime: new Date().toISOString(),
              duration: 45,
              description: 'Worked on initial draft and research',
              createdAt: new Date().toISOString()
            },
            {
              id: this.generateId(),
              taskId: sampleTasks[2].id,
              startTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
              endTime: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
              duration: 60,
              description: 'Studied React hooks and context API',
              createdAt: new Date().toISOString()
            }
          ];

          this.state.timeEntries = sampleTimeEntries;
          this.saveData();
        }
      }

      // ===== UTILITY FUNCTIONS =====
      generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
      }

      formatTime(minutes) {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${hours}h ${mins}m`;
      }

      formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      }

      debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
          const later = () => {
            clearTimeout(timeout);
            func(...args);
          };
          clearTimeout(timeout);
          timeout = setTimeout(later, wait);
        };
      }

      // ===== TIMER FUNCTIONALITY =====
      startTimer(taskId = null) {
        if (this.state.activeTimer) {
          this.showToast('Timer is already running', 'warning');
          return;
        }

        this.state.activeTimer = {
          taskId,
          startTime: Date.now(),
          elapsed: 0,
          isRunning: true
        };

        this.state.currentTask = taskId;
        this.updateTimerDisplay();
        this.updateTimerControls();

        this.timers.main = setInterval(() => {
          if (this.state.activeTimer && this.state.activeTimer.isRunning) {
            this.state.activeTimer.elapsed = Date.now() - this.state.activeTimer.startTime;
            this.updateTimerDisplay();
          }
        }, 1000);

        this.showToast('Timer started', 'success');
        this.saveData();
      }

      pauseTimer() {
        if (!this.state.activeTimer || !this.state.activeTimer.isRunning) {
          this.showToast('No active timer to pause', 'warning');
          return;
        }

        this.state.activeTimer.isRunning = false;
        this.state.activeTimer.pausedAt = Date.now();
        clearInterval(this.timers.main);

        this.updateTimerControls();
        this.showToast('Timer paused', 'info');
        this.saveData();
      }

      resumeTimer() {
        if (!this.state.activeTimer || this.state.activeTimer.isRunning) {
          this.showToast('No paused timer to resume', 'warning');
          return;
        }

        const pausedDuration = Date.now() - this.state.activeTimer.pausedAt;
        this.state.activeTimer.startTime += pausedDuration;
        this.state.activeTimer.isRunning = true;
        delete this.state.activeTimer.pausedAt;

        this.timers.main = setInterval(() => {
          if (this.state.activeTimer && this.state.activeTimer.isRunning) {
            this.state.activeTimer.elapsed = Date.now() - this.state.activeTimer.startTime;
            this.updateTimerDisplay();
          }
        }, 1000);

        this.updateTimerControls();
        this.showToast('Timer resumed', 'success');
        this.saveData();
      }

      stopTimer() {
        if (!this.state.activeTimer) {
          this.showToast('No active timer to stop', 'warning');
          return;
        }

        const duration = Math.floor(this.state.activeTimer.elapsed / 1000 / 60); // minutes
        const taskId = this.state.activeTimer.taskId;

        // Create time entry
        if (duration > 0) {
          const timeEntry = {
            id: this.generateId(),
            taskId,
            startTime: new Date(this.state.activeTimer.startTime).toISOString(),
            endTime: new Date().toISOString(),
            duration,
            description: '',
            createdAt: new Date().toISOString()
          };

          this.state.timeEntries.push(timeEntry);

          // Update task actual duration
          if (taskId) {
            const task = this.state.tasks.find(t => t.id === taskId);
            if (task) {
              task.actualDuration = (task.actualDuration || 0) + duration;
              task.updatedAt = new Date().toISOString();
            }
          }
        }

        // Clear timer
        clearInterval(this.timers.main);
        this.state.activeTimer = null;
        this.state.currentTask = null;

        this.updateTimerDisplay();
        this.updateTimerControls();
        this.updateAnalytics();
        this.updateUI();

        this.showToast(`Timer stopped. Logged ${this.formatTime(duration)}`, 'success');
        this.saveData();
      }

      updateTimerDisplay() {
        const display = document.getElementById('timerDisplay');
        const status = document.getElementById('timerStatus');

        if (this.state.activeTimer) {
          const seconds = Math.floor(this.state.activeTimer.elapsed / 1000);
          display.textContent = this.formatDuration(seconds);

          if (this.state.activeTimer.isRunning) {
            status.className = 'timer-status running';
            status.innerHTML = '<i class="fas fa-play-circle"></i> Running';
          } else {
            status.className = 'timer-status paused';
            status.innerHTML = '<i class="fas fa-pause-circle"></i> Paused';
          }
        } else {
          display.textContent = '00:00:00';
          status.className = 'timer-status stopped';
          status.innerHTML = '<i class="fas fa-stop-circle"></i> Stopped';
        }
      }

      updateTimerControls() {
        const startBtn = document.getElementById('startBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const stopBtn = document.getElementById('stopBtn');

        if (this.state.activeTimer) {
          if (this.state.activeTimer.isRunning) {
            startBtn.disabled = true;
            pauseBtn.disabled = false;
            stopBtn.disabled = false;
            startBtn.innerHTML = '<i class="fas fa-play"></i> Running';
          } else {
            startBtn.disabled = false;
            pauseBtn.disabled = true;
            stopBtn.disabled = false;
            startBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
          }
        } else {
          startBtn.disabled = false;
          pauseBtn.disabled = true;
          stopBtn.disabled = true;
          startBtn.innerHTML = '<i class="fas fa-play"></i> Start';
        }
      }

      // ===== CLOCK AND AUTO-SAVE =====
      startClockTimer() {
        this.updateClock();
        this.timers.clock = setInterval(() => {
          this.updateClock();
        }, 1000);
      }

      updateClock() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });

        const clockElement = document.getElementById('liveClock');
        if (clockElement) {
          clockElement.textContent = timeString;
        } else {
          console.warn('Live clock element not found');
        }
      }

      startAutoSave() {
        if (this.state.settings.autoSave) {
          this.timers.autoSave = setInterval(() => {
            this.saveData();
          }, 5 * 60 * 1000); // Save every 5 minutes
        }
      }

      // ===== EVENT LISTENERS =====
      setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.nav-tab').forEach(tab => {
          tab.addEventListener('click', (e) => {
            const tabName = e.target.closest('.nav-tab').dataset.tab;
            this.switchTab(tabName);
          });
        });

        // Timer controls
        document.getElementById('startBtn').addEventListener('click', () => {
          if (this.state.activeTimer && !this.state.activeTimer.isRunning) {
            this.resumeTimer();
          } else {
            const taskId = document.getElementById('activeTaskSelect').value;
            this.startTimer(taskId || null);
          }
        });

        document.getElementById('pauseBtn').addEventListener('click', () => {
          this.pauseTimer();
        });

        document.getElementById('stopBtn').addEventListener('click', () => {
          this.stopTimer();
        });

        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => {
          this.toggleTheme();
        });

        // Task management
        document.getElementById('addTaskBtn').addEventListener('click', () => {
          this.openTaskModal();
        });

        // Modal controls
        document.querySelectorAll('.modal-close').forEach(btn => {
          btn.addEventListener('click', (e) => {
            const modal = e.target.closest('.modal');
            this.closeModal(modal.id);
          });
        });

        // Task form
        document.getElementById('saveTaskBtn').addEventListener('click', () => {
          this.saveTask();
        });

        document.getElementById('cancelTaskBtn').addEventListener('click', () => {
          this.closeModal('taskModal');
        });

        // Floating action button
        document.getElementById('quickTimerBtn').addEventListener('click', () => {
          if (this.state.activeTimer) {
            if (this.state.activeTimer.isRunning) {
              this.pauseTimer();
            } else {
              this.resumeTimer();
            }
          } else {
            this.startTimer();
          }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
          if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
              case 'n':
                e.preventDefault();
                this.openTaskModal();
                break;
              case 's':
                e.preventDefault();
                this.saveData();
                this.showToast('Data saved', 'success');
                break;
              case 'd':
                e.preventDefault();
                this.toggleTheme();
                break;
            }
          }

          if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
            e.preventDefault();
            if (this.state.activeTimer) {
              if (this.state.activeTimer.isRunning) {
                this.pauseTimer();
              } else {
                this.resumeTimer();
              }
            } else {
              this.startTimer();
            }
          }
        });

        // Search and filters
        document.getElementById('searchTasks').addEventListener('input',
          this.debounce(() => this.renderTasks(), 300)
        );

        document.getElementById('filterStatus').addEventListener('change', () => {
          this.renderTasks();
        });

        document.getElementById('filterPriority').addEventListener('change', () => {
          this.renderTasks();
        });

        document.getElementById('filterCategory').addEventListener('change', () => {
          this.renderTasks();
        });

        // AI Insights refresh
        const refreshInsightsBtn = document.getElementById('refreshInsightsBtn');
        if (refreshInsightsBtn) {
          refreshInsightsBtn.addEventListener('click', () => {
            this.updateAIInsights();
            this.showToast('AI insights refreshed!', 'success');
          });
        }

        // Bulk actions
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');
        if (bulkActionsBtn) {
          bulkActionsBtn.addEventListener('click', () => {
            this.showToast('Bulk actions feature coming soon!', 'info');
          });
        }
      }

      // ===== UI MANAGEMENT =====
      switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.nav-tab').forEach(tab => {
          tab.classList.remove('active');
          tab.setAttribute('aria-selected', 'false');
        });

        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.querySelector(`[data-tab="${tabName}"]`).setAttribute('aria-selected', 'true');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.remove('active');
        });

        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;

        // Load tab-specific data
        switch (tabName) {
          case 'tasks':
            this.renderTasks();
            break;
          case 'analytics':
            this.updateAnalytics();
            this.updateCharts();
            break;
          case 'dashboard':
            this.updateDashboard();
            break;
        }
      }

      updateUI() {
        this.updateDashboard();
        this.renderTasks();
        this.updateTaskSelects();
        this.updateAnalytics();
        this.updateTimerDisplay();
        this.updateTimerControls();
      }

      updateDashboard() {
        // Calculate today's stats
        const today = new Date().toDateString();
        const todayEntries = this.state.timeEntries.filter(entry =>
          new Date(entry.startTime).toDateString() === today
        );

        const todayTime = todayEntries.reduce((total, entry) => total + entry.duration, 0);
        const completedTasks = this.state.tasks.filter(task => task.status === 'completed').length;
        const activeTasks = this.state.tasks.filter(task =>
          task.status === 'in-progress' || task.status === 'not-started'
        ).length;

        // Calculate efficiency (actual vs estimated)
        const tasksWithEstimates = this.state.tasks.filter(task =>
          task.estimatedDuration > 0 && task.actualDuration > 0
        );

        let efficiency = 0;
        if (tasksWithEstimates.length > 0) {
          const totalEstimated = tasksWithEstimates.reduce((sum, task) => sum + task.estimatedDuration, 0);
          const totalActual = tasksWithEstimates.reduce((sum, task) => sum + task.actualDuration, 0);
          efficiency = Math.round((totalEstimated / totalActual) * 100);
        }

        // Update UI
        document.getElementById('todayTime').textContent = this.formatTime(todayTime);
        document.getElementById('completedTasks').textContent = completedTasks;
        document.getElementById('activeTasks').textContent = activeTasks;
        document.getElementById('efficiency').textContent = `${efficiency}%`;

        // Update progress bar and goal progress
        const goalMinutes = this.state.settings.workingHours * 60;
        const progress = Math.min((todayTime / goalMinutes) * 100, 100);
        const progressBar = document.getElementById('todayProgress');
        if (progressBar) {
          progressBar.style.width = `${progress}%`;
        }

        // Update enhanced stats
        const goalProgressEl = document.getElementById('goalProgress');
        if (goalProgressEl) {
          goalProgressEl.textContent = `${Math.round(progress)}% of daily goal`;
        }

        // Update completion streak
        this.updateCompletionStreak();
        const completionStreakEl = document.getElementById('completionStreak');
        if (completionStreakEl) {
          completionStreakEl.textContent = `🔥 ${this.streaks.completion} day streak`;
        }

        // Update urgent tasks count
        const urgentTasks = this.state.tasks.filter(task =>
          task.priority === 'high' && task.status !== 'completed'
        ).length;
        const urgentTasksEl = document.getElementById('urgentTasks');
        if (urgentTasksEl) {
          urgentTasksEl.textContent = `${urgentTasks} urgent`;
        }

        // Update efficiency trend
        const efficiencyTrend = this.calculateEfficiencyTrend();
        const efficiencyTrendEl = document.getElementById('efficiencyTrend');
        if (efficiencyTrendEl) {
          const trendIcon = efficiencyTrend >= 0 ? '📈' : '📉';
          const trendText = efficiencyTrend >= 0 ? '+' : '';
          efficiencyTrendEl.textContent = `${trendIcon} ${trendText}${efficiencyTrend}% vs yesterday`;
        }

        // Update AI insights
        this.updateAIInsights();

        // Update recent activity
        this.updateRecentActivity();
      }

      updateRecentActivity() {
        const container = document.getElementById('recentActivity');
        const recentEntries = this.state.timeEntries
          .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
          .slice(0, 5);

        if (recentEntries.length === 0) {
          container.innerHTML = '<p class="text-center text-gray-500">No recent activity</p>';
          return;
        }

        container.innerHTML = recentEntries.map(entry => {
          const task = this.state.tasks.find(t => t.id === entry.taskId);
          const taskTitle = task ? task.title : 'Unknown Task';
          const startTime = new Date(entry.startTime).toLocaleTimeString();

          return `
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <div class="font-medium">${taskTitle}</div>
                <div class="text-sm text-gray-500">${entry.description || 'No description'}</div>
              </div>
              <div class="text-right">
                <div class="font-medium">${this.formatTime(entry.duration)}</div>
                <div class="text-sm text-gray-500">${startTime}</div>
              </div>
            </div>
          `;
        }).join('');
      }

      // ===== TASK MANAGEMENT =====
      openTaskModal(taskId = null) {
        const modal = document.getElementById('taskModal');
        const title = document.getElementById('taskModalTitle');

        if (taskId) {
          const task = this.state.tasks.find(t => t.id === taskId);
          if (task) {
            title.textContent = 'Edit Task';
            this.populateTaskForm(task);
          }
        } else {
          title.textContent = 'Add New Task';
          this.clearTaskForm();
        }

        modal.dataset.taskId = taskId || '';
        this.openModal('taskModal');
      }

      populateTaskForm(task) {
        document.getElementById('taskTitle').value = task.title;
        document.getElementById('taskDescription').value = task.description || '';
        document.getElementById('taskPriority').value = task.priority;
        document.getElementById('taskCategory').value = task.category;

        const hours = Math.floor(task.estimatedDuration / 60);
        const minutes = task.estimatedDuration % 60;
        document.getElementById('taskEstimateHours').value = hours;
        document.getElementById('taskEstimateMinutes').value = minutes;

        if (task.dueDate) {
          const dueDate = new Date(task.dueDate);
          document.getElementById('taskDueDate').value = dueDate.toISOString().slice(0, 16);
        }

        document.getElementById('taskTags').value = task.tags ? task.tags.join(', ') : '';
      }

      clearTaskForm() {
        document.getElementById('taskForm').reset();
        document.getElementById('taskPriority').value = 'medium';
        document.getElementById('taskCategory').value = 'work';
      }

      saveTask() {
        const form = document.getElementById('taskForm');
        const formData = new FormData(form);

        const title = document.getElementById('taskTitle').value.trim();
        if (!title) {
          this.showToast('Task title is required', 'error');
          return;
        }

        const hours = parseInt(document.getElementById('taskEstimateHours').value) || 0;
        const minutes = parseInt(document.getElementById('taskEstimateMinutes').value) || 0;
        const estimatedDuration = hours * 60 + minutes;

        const tags = document.getElementById('taskTags').value
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0);

        const taskData = {
          title,
          description: document.getElementById('taskDescription').value.trim(),
          priority: document.getElementById('taskPriority').value,
          category: document.getElementById('taskCategory').value,
          estimatedDuration,
          dueDate: document.getElementById('taskDueDate').value || null,
          tags,
          updatedAt: new Date().toISOString()
        };

        const modal = document.getElementById('taskModal');
        const taskId = modal.dataset.taskId;

        if (taskId) {
          // Update existing task
          const taskIndex = this.state.tasks.findIndex(t => t.id === taskId);
          if (taskIndex !== -1) {
            this.state.tasks[taskIndex] = { ...this.state.tasks[taskIndex], ...taskData };
            this.showToast('Task updated successfully', 'success');
          }
        } else {
          // Create new task
          const newTask = {
            id: this.generateId(),
            ...taskData,
            status: 'not-started',
            actualDuration: 0,
            createdAt: new Date().toISOString()
          };

          this.state.tasks.push(newTask);
          this.showToast('Task created successfully', 'success');
        }

        this.saveData();
        this.updateUI();
        this.closeModal('taskModal');
      }

      deleteTask(taskId) {
        const task = this.state.tasks.find(t => t.id === taskId);
        if (!task) return;

        this.showConfirmDialog(
          'Delete Task',
          `Are you sure you want to delete "${task.title}"? This action cannot be undone.`,
          () => {
            this.state.tasks = this.state.tasks.filter(t => t.id !== taskId);
            this.state.timeEntries = this.state.timeEntries.filter(e => e.taskId !== taskId);
            this.saveData();
            this.updateUI();
            this.showToast('Task deleted successfully', 'success');
          }
        );
      }

      updateTaskStatus(taskId, newStatus) {
        const task = this.state.tasks.find(t => t.id === taskId);
        if (task) {
          task.status = newStatus;
          task.updatedAt = new Date().toISOString();

          if (newStatus === 'completed') {
            task.completedAt = new Date().toISOString();
          }

          this.saveData();
          this.updateUI();
          this.showToast(`Task marked as ${newStatus.replace('-', ' ')}`, 'success');
        }
      }

      renderTasks() {
        const container = document.getElementById('taskList');
        const filteredTasks = this.getFilteredTasks();

        if (filteredTasks.length === 0) {
          container.innerHTML = `
            <div class="text-center py-12">
              <i class="fas fa-tasks text-4xl text-gray-400 mb-4"></i>
              <h3 class="text-lg font-medium text-gray-900 mb-2">No tasks found</h3>
              <p class="text-gray-500 mb-4">Create your first task to get started with time tracking.</p>
              <button type="button" class="btn btn-primary" onclick="app.openTaskModal()">
                <i class="fas fa-plus"></i>
                Add Task
              </button>
            </div>
          `;
          return;
        }

        container.innerHTML = filteredTasks.map(task => this.renderTaskItem(task)).join('');
      }

      renderTaskItem(task) {
        const dueDate = task.dueDate ? new Date(task.dueDate).toLocaleDateString() : 'No due date';
        const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && task.status !== 'completed';
        const progress = task.estimatedDuration > 0 ? Math.min((task.actualDuration / task.estimatedDuration) * 100, 100) : 0;

        return `
          <div class="task-item ${task.status} ${isOverdue ? 'overdue' : ''}" data-task-id="${task.id}">
            <div class="task-header">
              <div class="flex-1">
                <h3 class="task-title">${task.title}</h3>
                <div class="task-priority ${task.priority}">${task.priority.toUpperCase()}</div>
              </div>
            </div>

            <div class="task-meta">
              <div class="task-meta-item">
                <i class="fas fa-folder"></i>
                ${task.category}
              </div>
              <div class="task-meta-item">
                <i class="fas fa-clock"></i>
                ${this.formatTime(task.actualDuration)} / ${this.formatTime(task.estimatedDuration)}
              </div>
              <div class="task-meta-item">
                <i class="fas fa-calendar"></i>
                ${dueDate}
              </div>
            </div>

            ${task.description ? `<div class="task-description">${task.description}</div>` : ''}

            ${task.tags && task.tags.length > 0 ? `
              <div class="task-tags">
                ${task.tags.map(tag => `<span class="task-tag">${tag}</span>`).join('')}
              </div>
            ` : ''}

            <div class="progress">
              <div class="progress-bar" style="width: ${progress}%"></div>
            </div>

            <div class="task-actions">
              <button type="button" class="btn btn-sm btn-success" onclick="app.startTimer('${task.id}')">
                <i class="fas fa-play"></i>
                Start
              </button>

              <select class="form-select" style="width: auto;" onchange="app.updateTaskStatus('${task.id}', this.value)">
                <option value="not-started" ${task.status === 'not-started' ? 'selected' : ''}>Not Started</option>
                <option value="in-progress" ${task.status === 'in-progress' ? 'selected' : ''}>In Progress</option>
                <option value="paused" ${task.status === 'paused' ? 'selected' : ''}>Paused</option>
                <option value="completed" ${task.status === 'completed' ? 'selected' : ''}>Completed</option>
                <option value="cancelled" ${task.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
              </select>

              <button type="button" class="btn btn-sm btn-secondary" onclick="app.openTaskModal('${task.id}')">
                <i class="fas fa-edit"></i>
                Edit
              </button>

              <button type="button" class="btn btn-sm btn-danger" onclick="app.deleteTask('${task.id}')">
                <i class="fas fa-trash"></i>
                Delete
              </button>
            </div>
          </div>
        `;
      }

      getFilteredTasks() {
        let filtered = [...this.state.tasks];

        const statusFilter = document.getElementById('filterStatus').value;
        const priorityFilter = document.getElementById('filterPriority').value;
        const categoryFilter = document.getElementById('filterCategory').value;
        const searchTerm = document.getElementById('searchTasks').value.toLowerCase();

        if (statusFilter) {
          filtered = filtered.filter(task => task.status === statusFilter);
        }

        if (priorityFilter) {
          filtered = filtered.filter(task => task.priority === priorityFilter);
        }

        if (categoryFilter) {
          filtered = filtered.filter(task => task.category === categoryFilter);
        }

        if (searchTerm) {
          filtered = filtered.filter(task =>
            task.title.toLowerCase().includes(searchTerm) ||
            task.description.toLowerCase().includes(searchTerm) ||
            task.tags.some(tag => tag.toLowerCase().includes(searchTerm))
          );
        }

        return filtered;
      }



      updateTaskSelects() {
        const selects = ['activeTaskSelect', 'timeEntryTask', 'pomodoroTask'];
        const tasks = this.state.tasks.filter(task => task.status !== 'completed' && task.status !== 'cancelled');

        selects.forEach(selectId => {
          const select = document.getElementById(selectId);
          if (select) {
            const currentValue = select.value;
            select.innerHTML = '<option value="">Select a task...</option>';

            tasks.forEach(task => {
              const option = document.createElement('option');
              option.value = task.id;
              option.textContent = task.title;
              if (task.id === currentValue) {
                option.selected = true;
              }
              select.appendChild(option);
            });
          }
        });
      }

      // ===== ENHANCED ANALYTICS & AI INSIGHTS =====
      updateCompletionStreak() {
        const today = new Date();
        let streak = 0;

        for (let i = 0; i < 30; i++) {
          const checkDate = new Date(today);
          checkDate.setDate(today.getDate() - i);
          const dateString = checkDate.toDateString();

          const dayTasks = this.state.tasks.filter(task => {
            const taskDate = new Date(task.createdAt).toDateString();
            return taskDate === dateString && task.status === 'completed';
          });

          if (dayTasks.length > 0) {
            streak++;
          } else if (i > 0) {
            break;
          }
        }

        this.streaks.completion = streak;
      }

      calculateEfficiencyTrend() {
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);

        const todayEfficiency = this.calculateEfficiency();

        // Calculate yesterday's efficiency
        const yesterdayEntries = this.state.timeEntries.filter(entry =>
          new Date(entry.startTime).toDateString() === yesterday.toDateString()
        );

        if (yesterdayEntries.length === 0) return 0;

        const yesterdayTasks = yesterdayEntries.map(entry =>
          this.state.tasks.find(task => task.id === entry.taskId)
        ).filter(task => task && task.estimatedDuration > 0);

        if (yesterdayTasks.length === 0) return 0;

        const yesterdayEstimated = yesterdayTasks.reduce((sum, task) => sum + task.estimatedDuration, 0);
        const yesterdayActual = yesterdayEntries.reduce((sum, entry) => sum + entry.duration, 0);
        const yesterdayEfficiency = Math.round((yesterdayEstimated / yesterdayActual) * 100);

        return todayEfficiency - yesterdayEfficiency;
      }

      updateAIInsights() {
        // Generate AI-powered insights based on user data
        this.generateFocusInsight();
        this.generatePeakPerformanceInsight();
        this.generateWorkLifeBalanceInsight();

        // Update UI
        const focusEl = document.getElementById('focusInsight');
        const peakEl = document.getElementById('peakInsight');
        const balanceEl = document.getElementById('balanceInsight');

        if (focusEl) focusEl.textContent = this.aiInsights.focus;
        if (peakEl) peakEl.textContent = this.aiInsights.peak;
        if (balanceEl) balanceEl.textContent = this.aiInsights.balance;
      }

      generateFocusInsight() {
        const recentEntries = this.state.timeEntries
          .filter(entry => {
            const entryDate = new Date(entry.startTime);
            const daysDiff = (Date.now() - entryDate.getTime()) / (1000 * 60 * 60 * 24);
            return daysDiff <= 7;
          });

        if (recentEntries.length === 0) {
          this.aiInsights.focus = "Start tracking time to get personalized focus recommendations.";
          return;
        }

        // Analyze session lengths
        const avgSessionLength = recentEntries.reduce((sum, entry) => sum + entry.duration, 0) / recentEntries.length;

        if (avgSessionLength < 25) {
          this.aiInsights.focus = "Try the Pomodoro technique with 25-minute focused sessions for better concentration.";
        } else if (avgSessionLength > 90) {
          this.aiInsights.focus = "Consider taking breaks every 90 minutes to maintain peak mental performance.";
        } else {
          this.aiInsights.focus = "Your focus sessions are well-balanced. Keep up the great work!";
        }
      }

      generatePeakPerformanceInsight() {
        const hourlyData = {};

        this.state.timeEntries.forEach(entry => {
          const hour = new Date(entry.startTime).getHours();
          hourlyData[hour] = (hourlyData[hour] || 0) + entry.duration;
        });

        if (Object.keys(hourlyData).length === 0) {
          this.aiInsights.peak = "Track more sessions to identify your peak performance hours.";
          return;
        }

        const peakHour = Object.entries(hourlyData)
          .sort(([,a], [,b]) => b - a)[0][0];

        const timeFormat = peakHour < 12 ? `${peakHour}:00 AM` :
                          peakHour === 12 ? '12:00 PM' :
                          `${peakHour - 12}:00 PM`;

        this.aiInsights.peak = `Your peak productivity is around ${timeFormat}. Schedule important tasks during this time.`;
      }

      generateWorkLifeBalanceInsight() {
        const today = new Date();
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());

        const weekEntries = this.state.timeEntries.filter(entry => {
          const entryDate = new Date(entry.startTime);
          return entryDate >= weekStart;
        });

        const totalMinutes = weekEntries.reduce((sum, entry) => sum + entry.duration, 0);
        const totalHours = totalMinutes / 60;

        if (totalHours < 20) {
          this.aiInsights.balance = "You're maintaining a light work schedule. Consider if you need more focused time.";
        } else if (totalHours > 50) {
          this.aiInsights.balance = "You're working intensively. Remember to take breaks and maintain work-life balance.";
        } else {
          this.aiInsights.balance = "Your work schedule looks balanced. Great job maintaining healthy work habits!";
        }
      }

      // ===== ANALYTICS =====
      updateAnalytics() {
        this.calculateAnalytics();
        if (this.currentTab === 'analytics') {
          this.updateCharts();
        }
      }

      calculateAnalytics() {
        const today = new Date().toDateString();
        const todayEntries = this.state.timeEntries.filter(entry =>
          new Date(entry.startTime).toDateString() === today
        );

        this.state.analytics = {
          todayTime: todayEntries.reduce((total, entry) => total + entry.duration, 0),
          completedTasks: this.state.tasks.filter(task => task.status === 'completed').length,
          activeTasks: this.state.tasks.filter(task =>
            task.status === 'in-progress' || task.status === 'not-started'
          ).length,
          efficiency: this.calculateEfficiency()
        };
      }

      calculateEfficiency() {
        const tasksWithEstimates = this.state.tasks.filter(task =>
          task.estimatedDuration > 0 && task.actualDuration > 0
        );

        if (tasksWithEstimates.length === 0) return 0;

        const totalEstimated = tasksWithEstimates.reduce((sum, task) => sum + task.estimatedDuration, 0);
        const totalActual = tasksWithEstimates.reduce((sum, task) => sum + task.actualDuration, 0);

        return Math.round((totalEstimated / totalActual) * 100);
      }

      // ===== CHARTS =====
      initializeCharts() {
        // Time Distribution Chart
        const timeCtx = document.getElementById('timeDistributionChart').getContext('2d');
        this.charts.timeDistribution = new Chart(timeCtx, {
          type: 'doughnut',
          data: {
            labels: [],
            datasets: [{
              data: [],
              backgroundColor: [
                '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'
              ]
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'bottom'
              }
            }
          }
        });

        // Productivity Chart
        const prodCtx = document.getElementById('productivityChart').getContext('2d');
        this.charts.productivity = new Chart(prodCtx, {
          type: 'line',
          data: {
            labels: [],
            datasets: [{
              label: 'Daily Hours',
              data: [],
              borderColor: '#3b82f6',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      }

      updateCharts() {
        this.updateTimeDistributionChart();
        this.updateProductivityChart();
      }

      updateTimeDistributionChart() {
        const categoryData = {};

        this.state.timeEntries.forEach(entry => {
          const task = this.state.tasks.find(t => t.id === entry.taskId);
          const category = task ? task.category : 'Unknown';
          categoryData[category] = (categoryData[category] || 0) + entry.duration;
        });

        const labels = Object.keys(categoryData);
        const data = Object.values(categoryData);

        this.charts.timeDistribution.data.labels = labels;
        this.charts.timeDistribution.data.datasets[0].data = data;
        this.charts.timeDistribution.update();
      }

      updateProductivityChart() {
        const last7Days = [];
        const dailyHours = [];

        for (let i = 6; i >= 0; i--) {
          const date = new Date();
          date.setDate(date.getDate() - i);
          const dateString = date.toDateString();

          const dayEntries = this.state.timeEntries.filter(entry =>
            new Date(entry.startTime).toDateString() === dateString
          );

          const totalMinutes = dayEntries.reduce((sum, entry) => sum + entry.duration, 0);
          const hours = totalMinutes / 60;

          last7Days.push(date.toLocaleDateString('en-US', { weekday: 'short' }));
          dailyHours.push(hours);
        }

        this.charts.productivity.data.labels = last7Days;
        this.charts.productivity.data.datasets[0].data = dailyHours;
        this.charts.productivity.update();
      }

      // ===== THEME MANAGEMENT =====
      toggleTheme() {
        const currentTheme = this.state.settings.theme;
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.state.settings.theme = newTheme;
        this.applyTheme(newTheme);
        this.saveData();
      }

      applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        const themeToggle = document.getElementById('themeToggle');
        const icon = themeToggle.querySelector('i');
        const text = themeToggle.querySelector('span');

        if (theme === 'dark') {
          icon.className = 'fas fa-sun';
          text.textContent = 'Light';
        } else {
          icon.className = 'fas fa-moon';
          text.textContent = 'Dark';
        }
      }

      // ===== MODAL MANAGEMENT =====
      openModal(modalId) {
        const modal = document.getElementById(modalId);
        modal.classList.add('active');

        // Focus management
        const firstInput = modal.querySelector('input, textarea, select, button');
        if (firstInput) {
          setTimeout(() => firstInput.focus(), 100);
        }
      }

      closeModal(modalId) {
        const modal = document.getElementById(modalId);
        modal.classList.remove('active');
      }

      showConfirmDialog(title, message, onConfirm) {
        document.getElementById('confirmModalTitle').textContent = title;
        document.getElementById('confirmModalMessage').textContent = message;

        const confirmBtn = document.getElementById('confirmActionBtn');
        const cancelBtn = document.getElementById('confirmCancelBtn');

        const handleConfirm = () => {
          onConfirm();
          this.closeModal('confirmModal');
          confirmBtn.removeEventListener('click', handleConfirm);
          cancelBtn.removeEventListener('click', handleCancel);
        };

        const handleCancel = () => {
          this.closeModal('confirmModal');
          confirmBtn.removeEventListener('click', handleConfirm);
          cancelBtn.removeEventListener('click', handleCancel);
        };

        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);

        this.openModal('confirmModal');
      }

      // ===== NOTIFICATIONS =====
      showToast(message, type = 'info') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const icon = type === 'success' ? 'check-circle' :
                    type === 'error' ? 'exclamation-triangle' :
                    type === 'warning' ? 'exclamation-circle' : 'info-circle';

        toast.innerHTML = `
          <i class="fas fa-${icon}"></i>
          <span>${message}</span>
        `;

        container.appendChild(toast);

        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto remove
        setTimeout(() => {
          toast.classList.remove('show');
          setTimeout(() => {
            if (toast.parentNode) {
              toast.parentNode.removeChild(toast);
            }
          }, 300);
        }, 3000);
      }
    }

    // ===== APPLICATION INITIALIZATION =====
    let app;

    // Ensure DOM is fully loaded before initializing
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeApp);
    } else {
      // DOM is already loaded
      initializeApp();
    }

    function initializeApp() {
      try {
        app = new TimeTrackerApp();
        console.log('TimeTracker Pro application initialized successfully');
      } catch (error) {
        console.error('Failed to initialize TimeTracker Pro:', error);
        // Show error message to user
        document.body.innerHTML = `
          <div style="display: flex; align-items: center; justify-content: center; height: 100vh; flex-direction: column; font-family: system-ui;">
            <h1 style="color: #ef4444; margin-bottom: 1rem;">⚠️ Application Error</h1>
            <p style="color: #6b7280; text-align: center; max-width: 500px;">
              TimeTracker Pro failed to initialize. Please refresh the page or check the browser console for more details.
            </p>
            <button onclick="window.location.reload()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 0.5rem; cursor: pointer;">
              Refresh Page
            </button>
          </div>
        `;
      }
    }




  </script>
</body>
</html>